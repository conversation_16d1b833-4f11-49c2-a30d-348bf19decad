module.exports = {
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'zh'],
    localeDetection: true,
  },
  fallbackLng: {
    default: ['en'],
    zh: ['zh', 'en'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  // 命名空间配置
  ns: ['common'],
  defaultNS: 'common',
  
  // 插值配置
  interpolation: {
    escapeValue: false, // React已经处理了XSS
  },
  
  // 后端配置
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
  
  // 检测配置
  detection: {
    order: ['localStorage', 'navigator', 'htmlTag'],
    caches: ['localStorage'],
  },
};
