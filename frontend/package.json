{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "/opt/homebrew/opt/node@22/bin/node ../node_modules/.bin/next dev", "build": "/opt/homebrew/opt/node@22/bin/node ../node_modules/.bin/next build", "start": "/opt/homebrew/opt/node@22/bin/node ../node_modules/.bin/next start", "lint": "/opt/homebrew/opt/node@22/bin/node ../node_modules/.bin/next lint"}, "dependencies": {"@rainbow-me/rainbowkit": "^2.2.8", "@tanstack/react-query": "^5.81.5", "ethers": "^6.14.4", "merkletreejs": "^0.5.2", "next": "^15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "viem": "^2.31.6", "wagmi": "^2.15.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-next": "^15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}