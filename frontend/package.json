{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "/opt/homebrew/opt/node@22/bin/node ./node_modules/.bin/next dev", "build": "/opt/homebrew/opt/node@22/bin/node ./node_modules/.bin/next build", "start": "/opt/homebrew/opt/node@22/bin/node ./node_modules/.bin/next start", "lint": "/opt/homebrew/opt/node@22/bin/node ./node_modules/.bin/next lint"}, "dependencies": {"@rainbow-me/rainbowkit": "^2.2.8", "@tanstack/react-query": "^5.81.5", "ethers": "^6.14.4", "merkletreejs": "^0.5.2", "next": "^14.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "viem": "^2.31.4", "wagmi": "^2.15.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "14.2.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}