# AkashaDao Community Pass Frontend

这是AkashaDao社区通行证的前端应用，基于Next.js 15构建，集成了Web3功能。

## 功能特性

### 🎯 核心功能
- **钱包连接**: 支持多种钱包连接（MetaMask、WalletConnect等）
- **NFT铸造**: 白名单和学生两种类型的NFT铸造
- **白名单验证**: 基于Merkle Tree的白名单验证系统
- **NFT展示**: 用户NFT收藏展示和管理
- **权益查看**: NFT持有者权益和特权展示

### 🛠 技术栈
- **框架**: Next.js 15 (App Router)
- **样式**: Tailwind CSS 4
- **Web3**: Wagmi + Viem + RainbowKit
- **状态管理**: React Query (TanStack Query)
- **类型安全**: TypeScript
- **区块链**: 支持Ethereum、Sepolia测试网、Hardhat本地网络

## 项目结构

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── api/               # API路由
│   │   │   └── whitelist/     # 白名单验证API
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 主页
│   ├── components/            # React组件
│   │   ├── nft/              # NFT相关组件
│   │   │   ├── MintCard.tsx   # NFT铸造卡片
│   │   │   └── NFTCard.tsx    # NFT展示卡片
│   │   ├── providers/         # Context Providers
│   │   │   └── Web3Provider.tsx
│   │   └── wallet/            # 钱包相关组件
│   │       └── WalletButton.tsx
│   ├── hooks/                 # 自定义Hooks
│   │   └── useWhitelist.ts    # 白名单状态管理
│   └── lib/                   # 工具库
│       └── web3.ts            # Web3配置和工具
├── public/                    # 静态资源
└── package.json
```

## 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
npm start
```

## 环境配置

创建 `.env.local` 文件：

```env
# Web3配置
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id
NEXT_PUBLIC_AKASHA_NFT_ADDRESS=0x...
NEXT_PUBLIC_ACCESS_CONTROL_ADDRESS=0x...
NEXT_PUBLIC_MERKLE_GENERATOR_ADDRESS=0x...

# 网络配置
NEXT_PUBLIC_CHAIN_ID=31337
NEXT_PUBLIC_RPC_URL=http://localhost:8545
```

## 注意事项

- 确保使用Node.js 22版本
- 合约地址需要在部署后更新
- 白名单地址列表需要根据实际情况配置
- 生产环境需要配置正确的WalletConnect Project ID
