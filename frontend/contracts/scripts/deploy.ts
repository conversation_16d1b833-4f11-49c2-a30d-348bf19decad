import { ethers } from "hardhat";
import { writeFileSync } from "fs";
import { join } from "path";

async function main() {
  console.log("开始部署AkashaDao NFT合约...");

  // 获取部署账户
  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log("账户余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // NFT元数据URI（这里使用IPFS或者HTTP链接）
  const whitelistTokenURI = "https://akasha-dao.com/metadata/whitelist.json";
  const studentTokenURI = "https://akasha-dao.com/metadata/student.json";

  // 部署主NFT合约
  console.log("\n部署AkashaDaoNFT合约...");
  const AkashaDaoNFT = await ethers.getContractFactory("AkashaDaoNFT");
  const akashaNFT = await AkashaDaoNFT.deploy(whitelistTokenURI, studentTokenURI);
  await akashaNFT.waitForDeployment();
  const nftAddress = await akashaNFT.getAddress();
  console.log("AkashaDaoNFT合约地址:", nftAddress);

  // 部署访问控制合约
  console.log("\n部署AkashaDaoAccessControl合约...");
  const AkashaDaoAccessControl = await ethers.getContractFactory("AkashaDaoAccessControl");
  const accessControl = await AkashaDaoAccessControl.deploy(nftAddress);
  await accessControl.waitForDeployment();
  const accessControlAddress = await accessControl.getAddress();
  console.log("AkashaDaoAccessControl合约地址:", accessControlAddress);

  // 部署Merkle Tree生成器合约
  console.log("\n部署MerkleTreeGenerator合约...");
  const MerkleTreeGenerator = await ethers.getContractFactory("MerkleTreeGenerator");
  const merkleGenerator = await MerkleTreeGenerator.deploy();
  await merkleGenerator.waitForDeployment();
  const merkleGeneratorAddress = await merkleGenerator.getAddress();
  console.log("MerkleTreeGenerator合约地址:", merkleGeneratorAddress);

  // 等待几个区块确认
  console.log("\n等待区块确认...");
  await new Promise(resolve => setTimeout(resolve, 10000));

  // 验证合约部署
  console.log("\n验证合约部署状态...");
  try {
    const totalSupply = await akashaNFT.totalSupply();
    console.log("NFT总供应量:", totalSupply.toString());
    
    const whitelistPrice = await akashaNFT.whitelistPrice();
    console.log("白名单价格:", ethers.formatEther(whitelistPrice), "ETH");
    
    const publicPrice = await akashaNFT.publicPrice();
    console.log("公开价格:", ethers.formatEther(publicPrice), "ETH");
    
    const studentPrice = await akashaNFT.studentPrice();
    console.log("学生价格:", ethers.formatEther(studentPrice), "ETH");
    
    console.log("✅ 合约部署验证成功!");
  } catch (error) {
    console.error("❌ 合约验证失败:", error);
  }

  // 保存部署信息
  const deploymentInfo = {
    network: await ethers.provider.getNetwork(),
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    contracts: {
      AkashaDaoNFT: {
        address: nftAddress,
        constructorArgs: [whitelistTokenURI, studentTokenURI]
      },
      AkashaDaoAccessControl: {
        address: accessControlAddress,
        constructorArgs: [nftAddress]
      },
      MerkleTreeGenerator: {
        address: merkleGeneratorAddress,
        constructorArgs: []
      }
    },
    configuration: {
      whitelistTokenURI,
      studentTokenURI,
      maxWhitelistSupply: 100,
      whitelistMintPercentage: 80,
      lotteryPercentage: 20,
      maxMintPerAddress: 1,
      prices: {
        whitelist: ethers.formatEther(await akashaNFT.whitelistPrice()),
        public: ethers.formatEther(await akashaNFT.publicPrice()),
        student: ethers.formatEther(await akashaNFT.studentPrice())
      }
    }
  };

  // 写入部署信息到文件
  const deploymentPath = join(__dirname, "../deployments.json");
  writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
  console.log("\n📄 部署信息已保存到:", deploymentPath);

  // 输出前端需要的合约信息
  console.log("\n🔧 前端集成信息:");
  console.log("=".repeat(50));
  console.log("NFT合约地址:", nftAddress);
  console.log("访问控制合约地址:", accessControlAddress);
  console.log("Merkle生成器合约地址:", merkleGeneratorAddress);
  console.log("网络:", (await ethers.provider.getNetwork()).name);
  console.log("链ID:", (await ethers.provider.getNetwork()).chainId);

  // 输出环境变量格式
  console.log("\n📋 环境变量配置:");
  console.log("=".repeat(50));
  console.log(`NEXT_PUBLIC_NFT_CONTRACT_ADDRESS=${nftAddress}`);
  console.log(`NEXT_PUBLIC_ACCESS_CONTROL_ADDRESS=${accessControlAddress}`);
  console.log(`NEXT_PUBLIC_MERKLE_GENERATOR_ADDRESS=${merkleGeneratorAddress}`);
  console.log(`NEXT_PUBLIC_CHAIN_ID=${(await ethers.provider.getNetwork()).chainId}`);

  console.log("\n🎉 部署完成!");
  console.log("请保存上述合约地址用于前端集成。");
}

// 错误处理
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  });
