import { ethers } from "hardhat";
import { MerkleTree } from "merkletreejs";
import { writeFileSync, readFileSync, existsSync } from "fs";
import { join } from "path";

// 示例地址列表（实际使用时需要替换为真实地址）
const SAMPLE_ADDRESSES = {
  whitelist: [
    "******************************************",
    "0x8ba1f109551bD432803012645Hac136c22C177e9",
    "******************************************",
    "******************************************",
    "******************************************"
  ],
  lottery: [
    "0x111111111111111111111111111111111111111",
    "0x222222222222222222222222222222222222222",
    "0x333333333333333333333333333333333333333",
    "0x444444444444444444444444444444444444444",
    "0x555555555555555555555555555555555555555"
  ],
  student: [
    "0xaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
    "0xbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb",
    "0xccccccccccccccccccccccccccccccccccccccc",
    "0xddddddddddddddddddddddddddddddddddddddd",
    "0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"
  ]
};

interface MerkleData {
  root: string;
  tree: any;
  leaves: string[];
  proofs: { [address: string]: string[] };
}

interface AllMerkleData {
  whitelist: MerkleData;
  lottery: MerkleData;
  student: MerkleData;
  timestamp: string;
}

/**
 * 生成Merkle Tree
 */
function generateMerkleTree(addresses: string[]): MerkleData {
  console.log(`生成Merkle Tree，地址数量: ${addresses.length}`);
  
  // 生成叶子节点
  const leaves = addresses.map(addr => 
    ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
  );
  
  // 创建Merkle Tree
  const tree = new MerkleTree(leaves, ethers.keccak256, { sortPairs: true });
  const root = tree.getHexRoot();
  
  console.log(`Merkle Root: ${root}`);
  
  // 为每个地址生成proof
  const proofs: { [address: string]: string[] } = {};
  addresses.forEach((addr, index) => {
    const proof = tree.getHexProof(leaves[index]);
    proofs[addr] = proof;
    console.log(`地址 ${addr} 的proof: [${proof.join(", ")}]`);
  });
  
  return {
    root,
    tree: tree.toString(),
    leaves: leaves.map(leaf => leaf),
    proofs
  };
}

/**
 * 验证Merkle Proof
 */
function verifyProof(address: string, proof: string[], root: string): boolean {
  const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [address]));
  return MerkleTree.verify(proof, leaf, root, ethers.keccak256, { sortPairs: true });
}

/**
 * 从文件加载地址列表
 */
function loadAddressesFromFile(filePath: string): string[] {
  if (!existsSync(filePath)) {
    console.log(`文件不存在: ${filePath}，使用示例地址`);
    return [];
  }
  
  try {
    const content = readFileSync(filePath, 'utf8');
    const addresses = content.split('\n')
      .map(addr => addr.trim())
      .filter(addr => addr && ethers.isAddress(addr));
    
    console.log(`从 ${filePath} 加载了 ${addresses.length} 个有效地址`);
    return addresses;
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error);
    return [];
  }
}

/**
 * 主函数
 */
async function main() {
  console.log("开始生成Merkle Tree...");
  
  // 尝试从文件加载地址，如果文件不存在则使用示例地址
  const whitelistAddresses = loadAddressesFromFile(join(__dirname, "../data/whitelist.txt")) || SAMPLE_ADDRESSES.whitelist;
  const lotteryAddresses = loadAddressesFromFile(join(__dirname, "../data/lottery.txt")) || SAMPLE_ADDRESSES.lottery;
  const studentAddresses = loadAddressesFromFile(join(__dirname, "../data/student.txt")) || SAMPLE_ADDRESSES.student;
  
  console.log("\n=== 生成白名单Merkle Tree ===");
  const whitelistMerkle = generateMerkleTree(whitelistAddresses);
  
  console.log("\n=== 生成抽奖Merkle Tree ===");
  const lotteryMerkle = generateMerkleTree(lotteryAddresses);
  
  console.log("\n=== 生成学生Merkle Tree ===");
  const studentMerkle = generateMerkleTree(studentAddresses);
  
  // 验证一些proof
  console.log("\n=== 验证Merkle Proof ===");
  if (whitelistAddresses.length > 0) {
    const testAddr = whitelistAddresses[0];
    const testProof = whitelistMerkle.proofs[testAddr];
    const isValid = verifyProof(testAddr, testProof, whitelistMerkle.root);
    console.log(`白名单地址 ${testAddr} 验证结果: ${isValid ? '✅ 通过' : '❌ 失败'}`);
  }
  
  // 保存所有数据
  const allData: AllMerkleData = {
    whitelist: whitelistMerkle,
    lottery: lotteryMerkle,
    student: studentMerkle,
    timestamp: new Date().toISOString()
  };
  
  // 保存到文件
  const outputPath = join(__dirname, "../merkle-data.json");
  writeFileSync(outputPath, JSON.stringify(allData, null, 2));
  console.log(`\n📄 Merkle数据已保存到: ${outputPath}`);
  
  // 生成简化的前端使用数据
  const frontendData = {
    roots: {
      whitelist: whitelistMerkle.root,
      lottery: lotteryMerkle.root,
      student: studentMerkle.root
    },
    proofs: {
      whitelist: whitelistMerkle.proofs,
      lottery: lotteryMerkle.proofs,
      student: studentMerkle.proofs
    },
    timestamp: new Date().toISOString()
  };
  
  const frontendPath = join(__dirname, "../../frontend/public/merkle-data.json");
  writeFileSync(frontendPath, JSON.stringify(frontendData, null, 2));
  console.log(`📄 前端数据已保存到: ${frontendPath}`);
  
  // 输出合约设置命令
  console.log("\n🔧 合约设置命令:");
  console.log("=".repeat(50));
  console.log("// 设置白名单Merkle Root");
  console.log(`await akashaNFT.setWhitelistMerkleRoot("${whitelistMerkle.root}");`);
  console.log("\n// 设置抽奖Merkle Root");
  console.log(`await akashaNFT.setLotteryMerkleRoot("${lotteryMerkle.root}");`);
  console.log("\n// 设置学生Merkle Root");
  console.log(`await akashaNFT.setStudentMerkleRoot("${studentMerkle.root}");`);
  
  // 输出环境变量
  console.log("\n📋 环境变量:");
  console.log("=".repeat(50));
  console.log(`NEXT_PUBLIC_WHITELIST_MERKLE_ROOT=${whitelistMerkle.root}`);
  console.log(`NEXT_PUBLIC_LOTTERY_MERKLE_ROOT=${lotteryMerkle.root}`);
  console.log(`NEXT_PUBLIC_STUDENT_MERKLE_ROOT=${studentMerkle.root}`);
  
  console.log("\n🎉 Merkle Tree生成完成!");
  console.log("\n📝 使用说明:");
  console.log("1. 将真实的地址列表保存到 contracts/data/ 目录下的对应文件中");
  console.log("2. 重新运行此脚本生成真实的Merkle Tree");
  console.log("3. 使用输出的合约设置命令更新合约中的Merkle Root");
  console.log("4. 前端可以使用 merkle-data.json 中的proof数据进行验证");
}

// 创建数据目录和示例文件
function createSampleFiles() {
  const dataDir = join(__dirname, "../data");
  if (!existsSync(dataDir)) {
    require('fs').mkdirSync(dataDir, { recursive: true });
  }
  
  // 创建示例文件
  const files = [
    { name: "whitelist.txt", addresses: SAMPLE_ADDRESSES.whitelist },
    { name: "lottery.txt", addresses: SAMPLE_ADDRESSES.lottery },
    { name: "student.txt", addresses: SAMPLE_ADDRESSES.student }
  ];
  
  files.forEach(file => {
    const filePath = join(dataDir, file.name);
    if (!existsSync(filePath)) {
      writeFileSync(filePath, file.addresses.join('\n'));
      console.log(`创建示例文件: ${filePath}`);
    }
  });
}

// 运行
createSampleFiles();
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 生成失败:", error);
    process.exit(1);
  });
