{"name": "akasha-dao-contracts", "version": "1.0.0", "description": "AkashaDao NFT Smart Contracts", "main": "index.js", "scripts": {"build": "npx hardhat compile", "test": "npx hardhat test", "test:coverage": "npx hardhat coverage", "deploy:localhost": "npx hardhat run scripts/deploy.ts --network localhost", "deploy:goerli": "npx hardhat run scripts/deploy.ts --network goerli", "deploy:mainnet": "npx hardhat run scripts/deploy.ts --network mainnet", "verify:goerli": "npx hardhat verify --network goerli", "verify:mainnet": "npx hardhat verify --network mainnet", "node": "npx hardhat node", "clean": "npx hardhat clean", "size": "npx hardhat size-contracts", "lint": "npx solhint 'contracts/**/*.sol'", "lint:fix": "npx solhint 'contracts/**/*.sol' --fix"}, "keywords": ["nft", "ethereum", "solidity", "hardhat", "dao", "<PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.0.0", "@openzeppelin/contracts": "^5.3.0", "hardhat": "^2.25.0", "merkletreejs": "^0.5.2"}}