import { expect } from "chai";
import { ethers } from "hardhat";
import { AkashaDaoNFT, AkashaDaoAccessControl } from "../typechain-types";
import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { MerkleTree } from "merkletreejs";

describe("AkashaDaoAccessControl", function () {
  let akashaNFT: AkashaDaoNFT;
  let accessControl: AkashaDaoAccessControl;
  let owner: SignerWithAddress;
  let whitelistUser: SignerWithAddress;
  let studentUser: SignerWithAddress;
  let regularUser: SignerWithAddress;
  let addrs: SignerWithAddress[];

  let whitelistMerkleTree: MerkleTree;
  let studentMerkleTree: MerkleTree;

  const whitelistTokenURI = "https://akasha-dao.com/metadata/whitelist.json";
  const studentTokenURI = "https://akasha-dao.com/metadata/student.json";

  beforeEach(async function () {
    [owner, whitelistUser, studentUser, regularUser, ...addrs] = await ethers.getSigners();

    // 部署NFT合约
    const AkashaDaoNFT = await ethers.getContractFactory("AkashaDaoNFT");
    akashaNFT = await AkashaDaoNFT.deploy(whitelistTokenURI, studentTokenURI);
    await akashaNFT.waitForDeployment();

    // 部署访问控制合约
    const AkashaDaoAccessControl = await ethers.getContractFactory("AkashaDaoAccessControl");
    accessControl = await AkashaDaoAccessControl.deploy(await akashaNFT.getAddress());
    await accessControl.waitForDeployment();

    // 设置Merkle Trees
    const whitelistAddresses = [whitelistUser.address];
    const studentAddresses = [studentUser.address];

    const whitelistLeaves = whitelistAddresses.map(addr => 
      ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
    );
    whitelistMerkleTree = new MerkleTree(whitelistLeaves, ethers.keccak256, { sortPairs: true });

    const studentLeaves = studentAddresses.map(addr => 
      ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
    );
    studentMerkleTree = new MerkleTree(studentLeaves, ethers.keccak256, { sortPairs: true });

    await akashaNFT.setWhitelistMerkleRoot(whitelistMerkleTree.getHexRoot());
    await akashaNFT.setStudentMerkleRoot(studentMerkleTree.getHexRoot());

    // Mint一些NFT用于测试
    await akashaNFT.setWhitelistSaleActive(true);
    await akashaNFT.setStudentSaleActive(true);

    // 白名单用户mint
    const whitelistLeaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser.address]));
    const whitelistProof = whitelistMerkleTree.getHexProof(whitelistLeaf);
    await akashaNFT.connect(whitelistUser).whitelistMint(whitelistProof, {
      value: ethers.parseEther("0.05")
    });

    // 学生用户mint
    const studentLeaf = ethers.keccak256(ethers.solidityPacked(["address"], [studentUser.address]));
    const studentProof = studentMerkleTree.getHexProof(studentLeaf);
    await akashaNFT.connect(studentUser).studentMint(studentProof, {
      value: ethers.parseEther("0.03")
    });
  });

  describe("部署", function () {
    it("应该正确设置NFT合约地址", async function () {
      expect(await accessControl.akashaNFT()).to.equal(await akashaNFT.getAddress());
    });

    it("应该正确设置owner", async function () {
      expect(await accessControl.owner()).to.equal(owner.address);
    });
  });

  describe("访问权限验证", function () {
    it("应该正确验证白名单成员的无限阅读权限", async function () {
      expect(await accessControl.hasAccess(whitelistUser.address, 0)).to.be.true; // UNLIMITED_READING
    });

    it("应该正确验证白名单成员的投票权", async function () {
      expect(await accessControl.hasAccess(whitelistUser.address, 1)).to.be.true; // VOTING_RIGHTS
    });

    it("应该正确验证白名单成员的专属活动权限", async function () {
      expect(await accessControl.hasAccess(whitelistUser.address, 2)).to.be.true; // EXCLUSIVE_EVENTS
    });

    it("应该正确验证学生成员的无限阅读权限", async function () {
      expect(await accessControl.hasAccess(studentUser.address, 0)).to.be.true; // UNLIMITED_READING
    });

    it("应该拒绝学生成员的投票权", async function () {
      expect(await accessControl.hasAccess(studentUser.address, 1)).to.be.false; // VOTING_RIGHTS
    });

    it("应该拒绝学生成员的专属活动权限", async function () {
      expect(await accessControl.hasAccess(studentUser.address, 2)).to.be.false; // EXCLUSIVE_EVENTS
    });

    it("应该允许所有NFT持有者的VC对接权限", async function () {
      expect(await accessControl.hasAccess(whitelistUser.address, 3)).to.be.true; // VC_NETWORKING
      expect(await accessControl.hasAccess(studentUser.address, 3)).to.be.true; // VC_NETWORKING
    });

    it("应该拒绝非NFT持有者的所有权限", async function () {
      expect(await accessControl.hasAccess(regularUser.address, 0)).to.be.false;
      expect(await accessControl.hasAccess(regularUser.address, 1)).to.be.false;
      expect(await accessControl.hasAccess(regularUser.address, 2)).to.be.false;
      expect(await accessControl.hasAccess(regularUser.address, 3)).to.be.false;
    });
  });

  describe("成员身份验证", function () {
    it("应该正确识别白名单成员", async function () {
      expect(await accessControl.isWhitelistMember(whitelistUser.address)).to.be.true;
      expect(await accessControl.isWhitelistMember(studentUser.address)).to.be.false;
      expect(await accessControl.isWhitelistMember(regularUser.address)).to.be.false;
    });

    it("应该正确识别学生成员", async function () {
      expect(await accessControl.isStudentMember(studentUser.address)).to.be.true;
      expect(await accessControl.isStudentMember(whitelistUser.address)).to.be.false;
      expect(await accessControl.isStudentMember(regularUser.address)).to.be.false;
    });
  });

  describe("活动管理", function () {
    it("应该允许owner创建活动", async function () {
      const startTime = Math.floor(Date.now() / 1000) + 3600; // 1小时后
      const endTime = startTime + 7200; // 2小时后

      await expect(
        accessControl.createEvent(
          "Test Event",
          "Test Description",
          startTime,
          endTime,
          true // 需要白名单
        )
      ).to.emit(accessControl, "EventCreated")
       .withArgs(0, "Test Event");

      expect(await accessControl.eventCounter()).to.equal(1);
    });

    it("应该拒绝非owner创建活动", async function () {
      const startTime = Math.floor(Date.now() / 1000) + 3600;
      const endTime = startTime + 7200;

      await expect(
        accessControl.connect(regularUser).createEvent(
          "Test Event",
          "Test Description",
          startTime,
          endTime,
          false
        )
      ).to.be.revertedWithCustomError(accessControl, "OwnableUnauthorizedAccount");
    });

    it("应该允许符合条件的用户参与活动", async function () {
      const startTime = Math.floor(Date.now() / 1000) - 3600; // 1小时前开始
      const endTime = Math.floor(Date.now() / 1000) + 3600; // 1小时后结束

      // 创建需要白名单的活动
      await accessControl.createEvent(
        "Whitelist Event",
        "Event for whitelist members",
        startTime,
        endTime,
        true
      );

      await expect(
        accessControl.connect(whitelistUser).participateInEvent(0)
      ).to.emit(accessControl, "EventParticipation")
       .withArgs(0, whitelistUser.address);
    });

    it("应该拒绝不符合条件的用户参与活动", async function () {
      const startTime = Math.floor(Date.now() / 1000) - 3600;
      const endTime = Math.floor(Date.now() / 1000) + 3600;

      // 创建需要白名单的活动
      await accessControl.createEvent(
        "Whitelist Event",
        "Event for whitelist members",
        startTime,
        endTime,
        true
      );

      await expect(
        accessControl.connect(studentUser).participateInEvent(0)
      ).to.be.revertedWith("Whitelist membership required");
    });
  });

  describe("提案和投票", function () {
    it("应该允许白名单成员创建提案", async function () {
      const votingPeriod = 7200; // 2小时

      await expect(
        accessControl.connect(whitelistUser).createProposal(
          "Test Proposal",
          "Test proposal description",
          votingPeriod
        )
      ).to.emit(accessControl, "ProposalCreated")
       .withArgs(0, "Test Proposal");

      expect(await accessControl.proposalCounter()).to.equal(1);
    });

    it("应该拒绝非白名单成员创建提案", async function () {
      const votingPeriod = 7200;

      await expect(
        accessControl.connect(studentUser).createProposal(
          "Test Proposal",
          "Test proposal description",
          votingPeriod
        )
      ).to.be.revertedWith("Only whitelist members can create proposals");
    });

    it("应该允许有投票权的用户投票", async function () {
      const votingPeriod = 7200;

      // 创建提案
      await accessControl.connect(whitelistUser).createProposal(
        "Test Proposal",
        "Test proposal description",
        votingPeriod
      );

      // 投票
      await expect(
        accessControl.connect(whitelistUser).vote(0, true)
      ).to.emit(accessControl, "VoteCast")
       .withArgs(0, whitelistUser.address, true);

      const [forVotes, againstVotes] = await accessControl.getProposalResults(0);
      expect(forVotes).to.equal(1);
      expect(againstVotes).to.equal(0);
    });

    it("应该拒绝无投票权的用户投票", async function () {
      const votingPeriod = 7200;

      // 创建提案
      await accessControl.connect(whitelistUser).createProposal(
        "Test Proposal",
        "Test proposal description",
        votingPeriod
      );

      // 学生用户尝试投票
      await expect(
        accessControl.connect(studentUser).vote(0, true)
      ).to.be.revertedWith("No voting rights");
    });

    it("应该拒绝重复投票", async function () {
      const votingPeriod = 7200;

      // 创建提案
      await accessControl.connect(whitelistUser).createProposal(
        "Test Proposal",
        "Test proposal description",
        votingPeriod
      );

      // 第一次投票
      await accessControl.connect(whitelistUser).vote(0, true);

      // 第二次投票应该失败
      await expect(
        accessControl.connect(whitelistUser).vote(0, false)
      ).to.be.revertedWith("Already voted");
    });
  });

  describe("访问时间更新", function () {
    it("应该允许NFT持有者更新访问时间", async function () {
      await accessControl.connect(whitelistUser).updateAccessTime(whitelistUser.address);
      
      const lastAccessTime = await accessControl.lastAccessTime(whitelistUser.address);
      expect(lastAccessTime).to.be.greaterThan(0);
    });

    it("应该拒绝非NFT持有者更新访问时间", async function () {
      await expect(
        accessControl.connect(regularUser).updateAccessTime(regularUser.address)
      ).to.be.revertedWith("NFT ownership required");
    });
  });
});
