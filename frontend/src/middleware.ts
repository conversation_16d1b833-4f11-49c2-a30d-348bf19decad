import { NextRequest, NextResponse } from 'next/server';

const locales = ['en', 'zh'];
const defaultLocale = 'en';

// 获取首选语言
function getLocale(request: NextRequest): string {
  // 1. 检查URL中的语言参数
  const pathname = request.nextUrl.pathname;
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameHasLocale) {
    return pathname.split('/')[1];
  }

  // 2. 检查cookie中的语言设置
  const localeCookie = request.cookies.get('NEXT_LOCALE')?.value;
  if (localeCookie && locales.includes(localeCookie)) {
    return localeCookie;
  }

  // 3. 检查Accept-Language头
  const acceptLanguage = request.headers.get('Accept-Language');
  if (acceptLanguage) {
    const preferredLocale = acceptLanguage
      .split(',')
      .map((lang) => lang.split(';')[0].trim())
      .find((lang) => {
        return locales.some((locale) => 
          lang.toLowerCase().startsWith(locale.toLowerCase())
        );
      });
    
    if (preferredLocale) {
      const matchedLocale = locales.find((locale) =>
        preferredLocale.toLowerCase().startsWith(locale.toLowerCase())
      );
      if (matchedLocale) {
        return matchedLocale;
      }
    }
  }

  return defaultLocale;
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 检查路径是否已经包含语言代码
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // 如果路径没有语言代码，重定向到带语言代码的路径
  if (!pathnameHasLocale) {
    const locale = getLocale(request);
    const newUrl = new URL(`/${locale}${pathname}`, request.url);
    
    // 设置语言cookie
    const response = NextResponse.redirect(newUrl);
    response.cookies.set('NEXT_LOCALE', locale, {
      maxAge: 60 * 60 * 24 * 365, // 1年
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
    });
    
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // 匹配所有路径，除了以下路径：
    '/((?!api|_next/static|_next/image|favicon.ico|images|locales).*)',
  ],
};
