@import "tailwindcss";

@theme {
  --color-background: #ffffff;
  --color-foreground: #171717;

  --animate-spin-slow: spin 3s linear infinite;
  --animate-pulse-slow: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce-slow: bounce 2s infinite;
  --animate-glow: glow 2s ease-in-out infinite alternate;
  --animate-float: float 3s ease-in-out infinite;
  --animate-fade-in: fadeIn 1s ease-out;
  --animate-slide-up: slideUp 0.8s ease-out;
  --animate-scale-in: scaleIn 0.5s ease-out;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5), 0 0 10px rgba(139, 92, 246, 0.3), 0 0 15px rgba(139, 92, 246, 0.1);
  }
  100% {
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.8), 0 0 20px rgba(139, 92, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(30px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: #0a0a0a;
    --color-foreground: #ededed;
  }
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-inter), Arial, Helvetica, sans-serif;
}
