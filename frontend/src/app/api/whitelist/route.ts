import { NextRequest, NextResponse } from 'next/server';
import { keccak256 } from 'viem';
import { MerkleTree } from 'merkletreejs';

// 模拟白名单地址（实际应用中应该从数据库获取）
const WHITELIST_ADDRESSES = [
  '0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266',
  '0x70997970C51812dc3A010C7d01b50e0d17dc79C8',
  '0x3C44CdDdB6a900fa2b585dd299e03d12FA4293BC',
  '0x90F79bf6EB2c4f870365E785982E1f101E93b906',
  '0x15d34AAf54267DB7D7c367839AAf71A00a2C6A65',
];

const STUDENT_ADDRESSES = [
  '0x9965507D1a55bcC2695C58ba16FB37d819B0A4dc',
  '0x976EA74026E726554dB657fA54763abd0C3a0aa9',
  '0x14dC79964da2C08b23698B3D3cc7Ca32193d9955',
  '0x23618e81E3f5cdF7f54C3d65f7FBc0aBf5B21E8f',
  '******************************************',
];

// 生成Merkle Tree
function generateMerkleTree(addresses: string[]): MerkleTree {
  const leaves = addresses.map(addr => keccak256(addr as `0x${string}`));
  return new MerkleTree(leaves, keccak256, { sortPairs: true });
}

// 获取Merkle Proof
function getMerkleProof(addresses: string[], targetAddress: string): string[] {
  const tree = generateMerkleTree(addresses);
  const leaf = keccak256(targetAddress as `0x${string}`);
  return tree.getHexProof(leaf);
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const address = searchParams.get('address');
  const type = searchParams.get('type'); // 'whitelist' or 'student'

  if (!address) {
    return NextResponse.json(
      { error: '地址参数缺失' },
      { status: 400 }
    );
  }

  try {
    let isEligible = false;
    let merkleProof: string[] = [];
    let merkleRoot = '';

    if (type === 'whitelist') {
      isEligible = WHITELIST_ADDRESSES.includes(address.toLowerCase());
      if (isEligible) {
        merkleProof = getMerkleProof(WHITELIST_ADDRESSES, address);
        const tree = generateMerkleTree(WHITELIST_ADDRESSES);
        merkleRoot = tree.getHexRoot();
      }
    } else if (type === 'student') {
      isEligible = STUDENT_ADDRESSES.includes(address.toLowerCase());
      if (isEligible) {
        merkleProof = getMerkleProof(STUDENT_ADDRESSES, address);
        const tree = generateMerkleTree(STUDENT_ADDRESSES);
        merkleRoot = tree.getHexRoot();
      }
    } else {
      return NextResponse.json(
        { error: '无效的类型参数' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      address,
      type,
      isEligible,
      merkleProof,
      merkleRoot,
    });
  } catch (error) {
    console.error('Whitelist verification error:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// 获取所有白名单和学生名单的Merkle Root
export async function POST() {
  try {
    const whitelistTree = generateMerkleTree(WHITELIST_ADDRESSES);
    const studentTree = generateMerkleTree(STUDENT_ADDRESSES);

    return NextResponse.json({
      whitelist: {
        addresses: WHITELIST_ADDRESSES,
        merkleRoot: whitelistTree.getHexRoot(),
        totalCount: WHITELIST_ADDRESSES.length,
      },
      student: {
        addresses: STUDENT_ADDRESSES,
        merkleRoot: studentTree.getHexRoot(),
        totalCount: STUDENT_ADDRESSES.length,
      },
    });
  } catch (error) {
    console.error('Merkle tree generation error:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
