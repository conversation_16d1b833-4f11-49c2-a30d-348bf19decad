'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { useTranslation } from '@/components/providers/I18nProvider';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Logo } from '@/components/ui/Logo';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';

export default function Home() {
  const { address, isConnected } = useAccount();
  const { t, tArray } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 动态背景效果
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.zIndex = '1';
    canvas.style.pointerEvents = 'none';
    document.body.appendChild(canvas);

    return () => {
      if (document.body.contains(canvas)) {
        document.body.removeChild(canvas);
      }
    };
  }, []);

  return (
    <>
      <div className="min-h-screen bg-black overflow-hidden relative">
        {/* 动态背景 */}
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
        </div>

        {/* 导航栏 */}
        <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-20">
              <Logo size="lg" showText={true} showSubtitle={true} href="/" />

              {/* 导航菜单 */}
              <div className="hidden md:flex items-center space-x-8">
                <a href="#about" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.about')}</a>
                <a href="#tiers" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">分层体系</a>
                <a href="#benefits" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">权益</a>
                <a href="#roadmap" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.roadmap')}</a>
                <a href="#community" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.community')}</a>
              </div>

              <div className="flex items-center space-x-4">
                <LanguageSwitcher />
                <a
                  href="/mint"
                  className="px-8 py-3 bg-gradient-to-r from-purple-600 via-purple-500 to-blue-600 hover:from-purple-700 hover:via-purple-600 hover:to-blue-700 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/25 border border-purple-400/30"
                >
                  {t('nav.mint')}
                </a>
                <WalletButton />
              </div>
            </div>
          </div>
        </nav>

        {/* 主要内容 - 统一的能量流动设计系统 */}
        <main className="relative z-10">
          {/* 全局能量流动背景 */}
          <div className="fixed inset-0 pointer-events-none z-0">
            <svg width="100%" height="100%" className="absolute inset-0">
              <defs>
                <linearGradient id="energyFlow" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.1" />
                  <stop offset="25%" stopColor="#06B6D4" stopOpacity="0.1" />
                  <stop offset="50%" stopColor="#10B981" stopOpacity="0.1" />
                  <stop offset="75%" stopColor="#F59E0B" stopOpacity="0.1" />
                  <stop offset="100%" stopColor="#EF4444" stopOpacity="0.1" />
                </linearGradient>
                <radialGradient id="energyCore" cx="50%" cy="20%" r="30%">
                  <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                  <stop offset="50%" stopColor="#06B6D4" stopOpacity="0.2" />
                  <stop offset="100%" stopColor="transparent" />
                </radialGradient>
              </defs>
              {/* 主能量流 - 连接所有部分的脊柱 */}
              <path
                d="M 50 100 Q 200 300 50 500 Q 200 700 50 900 Q 200 1100 50 1300 Q 200 1500 50 1700"
                stroke="url(#energyFlow)"
                strokeWidth="3"
                fill="none"
                strokeDasharray="20,10"
                className="animate-pulse"
              />
              <path
                d="M 950 100 Q 800 300 950 500 Q 800 700 950 900 Q 800 1100 950 1300 Q 800 1500 950 1700"
                stroke="url(#energyFlow)"
                strokeWidth="3"
                fill="none"
                strokeDasharray="20,10"
                className="animate-pulse"
                style={{animationDelay: '1s'}}
              />
              {/* 中央能量核心 */}
              <circle cx="50%" cy="20%" r="200" fill="url(#energyCore)" className="animate-pulse" />
            </svg>
          </div>

          {/* Hero 区域 - 能量核心 */}
          <section className="min-h-screen relative overflow-hidden pt-20 flex items-center justify-center">
            {/* 中央能量核心 */}
            <div className="relative group">
              {/* 核心能量场 */}
              <div className="absolute -inset-16 bg-gradient-to-r from-purple-500/20 via-cyan-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute -inset-12 bg-gradient-to-r from-cyan-500/15 via-purple-500/15 to-blue-500/15 rounded-full blur-2xl animate-pulse" style={{animationDelay: '0.5s'}}></div>
              <div className="absolute -inset-8 bg-gradient-to-r from-pink-500/10 via-yellow-500/10 to-purple-500/10 rounded-full blur-xl animate-pulse" style={{animationDelay: '1s'}}></div>

              {/* 主核心 */}
              <div className="relative bg-black/70 backdrop-blur-2xl rounded-full p-16 border-2 border-white/20 group-hover:border-white/40 transition-all duration-1000">
                <div className="text-center">
                  <h1 className="text-6xl md:text-8xl font-black text-white mb-6">
                    <span className="bg-gradient-to-r from-purple-300 via-cyan-300 to-pink-300 bg-clip-text text-transparent animate-pulse">
                      AKASHA
                    </span>
                    <br />
                    <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent animate-pulse" style={{animationDelay: '0.5s'}}>
                      DAO
                    </span>
                  </h1>
                  <p className="text-white/90 text-xl mb-8 font-light">神秘组织 · 信任象征 · 能量汇聚</p>
                  <a
                    href="/mint"
                    className="inline-block px-12 py-6 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 rounded-full font-bold text-white text-lg transition-all duration-500 hover:scale-110 shadow-2xl shadow-purple-500/50 hover:shadow-cyan-500/50"
                  >
                    启动能量铸造
                  </a>
                </div>
              </div>

              {/* 环绕能量轨道 */}
              <div className="absolute inset-0 animate-spin" style={{animationDuration: '60s'}}>
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-purple-500 rounded-full blur-sm opacity-60"></div>
                <div className="absolute top-1/2 -right-4 transform -translate-y-1/2 w-6 h-6 bg-cyan-500 rounded-full blur-sm opacity-60"></div>
                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-pink-500 rounded-full blur-sm opacity-60"></div>
                <div className="absolute top-1/2 -left-4 transform -translate-y-1/2 w-6 h-6 bg-yellow-500 rounded-full blur-sm opacity-60"></div>
              </div>
            </div>

            {/* 能量射线指向下一部分 */}
            <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2">
              <div className="w-1 h-20 bg-gradient-to-b from-purple-500/50 to-transparent animate-pulse"></div>
              <div className="w-3 h-3 bg-purple-500 rounded-full mx-auto animate-bounce"></div>
            </div>
          </section>

          {/* 分层体系区域 - 能量层级分布 */}
          <section id="tiers" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            {/* 能量接收器 - 从上方Hero接收能量 */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-6 h-6 bg-purple-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-16 bg-gradient-to-b from-purple-500 to-transparent mx-auto"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent">
                    能量层级
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  能量在不同层级间流动，构建有机的社区生态系统
                </p>
              </div>

              {/* 能量层级分布 - 垂直流动设计 */}
              <div className="relative flex flex-col items-center space-y-16">

                {/* 顶层能量节点 - 白名单层 */}
                <div className="relative group">
                  {/* 能量场 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-cyan-500/20 rounded-full blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 min-w-[400px]">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <span className="text-white font-bold text-2xl">👑</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-3">白名单层</h3>
                      <p className="text-purple-300 mb-4">精英投资人 · 最高能量级</p>
                      <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg p-4">
                        <div className="text-3xl font-bold text-white mb-1">0.08 ETH</div>
                        <div className="text-purple-300 text-sm">铸造价格</div>
                      </div>
                    </div>
                  </div>

                  {/* 能量传输线 */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-16 bg-gradient-to-b from-purple-500/50 to-cyan-500/50 animate-pulse"></div>
                  </div>
                </div>

                {/* 中层能量节点 - 学生层 */}
                <div className="relative group">
                  {/* 能量场 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-green-500/20 rounded-full blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-cyan-500/50 group-hover:border-cyan-500/80 transition-all duration-500 min-w-[450px]">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <span className="text-white font-bold text-2xl">🎓</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-3">学生层</h3>
                      <p className="text-cyan-300 mb-4">创业者社区 · 中等能量级</p>
                      <div className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-lg p-4">
                        <div className="text-3xl font-bold text-white mb-1">0.05 ETH</div>
                        <div className="text-cyan-300 text-sm">铸造价格</div>
                      </div>
                    </div>
                  </div>

                  {/* 能量传输线 */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-16 bg-gradient-to-b from-cyan-500/50 to-green-500/50 animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  </div>
                </div>

                {/* 底层能量节点 - 公开层 */}
                <div className="relative group">
                  {/* 能量场 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-green-500/20 via-yellow-500/20 to-orange-500/20 rounded-full blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500 min-w-[500px]">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <span className="text-white font-bold text-2xl">🌍</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-3">公开层</h3>
                      <p className="text-green-300 mb-4">社区成员 · 基础能量级</p>
                      <div className="bg-gradient-to-r from-green-500/20 to-yellow-500/20 rounded-lg p-4">
                        <div className="text-3xl font-bold text-white mb-1">0.03 ETH</div>
                        <div className="text-green-300 text-sm">铸造价格</div>
                      </div>
                    </div>
                  </div>

                  {/* 能量传输到下一部分 */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-16 bg-gradient-to-b from-green-500/50 to-transparent animate-pulse" style={{animationDelay: '1s'}}></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full mx-auto animate-bounce"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 核心价值区域 - 能量共振网络 */}
          <section id="about" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            {/* 能量接收器 */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-6 h-6 bg-green-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-16 bg-gradient-to-b from-green-500 to-transparent mx-auto"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                    能量共振
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  四大核心价值形成能量共振网络，驱动整个生态系统的持续发展
                </p>
              </div>

              {/* 能量共振网络 - 四象限布局 */}
              <div className="relative min-h-[600px] flex items-center justify-center">

                {/* 中央能量核心 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
                  <div className="relative">
                    <div className="absolute -inset-6 bg-gradient-to-r from-purple-500/30 via-cyan-500/30 to-green-500/30 rounded-full blur-2xl animate-pulse"></div>
                    <div className="relative w-16 h-16 bg-gradient-to-r from-purple-500 via-cyan-500 to-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xl">⚡</span>
                    </div>
                  </div>
                </div>

                {/* 上方能量节点 - 神秘IP */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 min-w-[300px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">🎭</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">神秘IP</h3>
                      <p className="text-purple-300 text-sm">维持神秘人设 · 信任为锚点</p>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-12 bg-gradient-to-b from-purple-500/50 to-transparent animate-pulse"></div>
                  </div>
                </div>

                {/* 右方能量节点 - 价值共创 */}
                <div className="absolute top-1/2 right-0 transform -translate-y-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-cyan-500/50 group-hover:border-cyan-500/80 transition-all duration-500 min-w-[300px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">💎</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">价值共创</h3>
                      <p className="text-cyan-300 text-sm">社区共建 · 价值共享</p>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute top-1/2 right-full transform -translate-y-1/2">
                    <div className="w-12 h-1 bg-gradient-to-l from-cyan-500/50 to-transparent animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  </div>
                </div>

                {/* 下方能量节点 - 生态发展 */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500 min-w-[300px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-green-500/20 to-yellow-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">🌟</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">生态发展</h3>
                      <p className="text-green-300 text-sm">持续创新 · 生态繁荣</p>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-12 bg-gradient-to-t from-green-500/50 to-transparent animate-pulse" style={{animationDelay: '1s'}}></div>
                  </div>
                </div>

                {/* 左方能量节点 - 信任为本 */}
                <div className="absolute top-1/2 left-0 transform -translate-y-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500 min-w-[300px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">🤝</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">信任为本</h3>
                      <p className="text-yellow-300 text-sm">持续价值输出 · 维护社区信任</p>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute top-1/2 left-full transform -translate-y-1/2">
                    <div className="w-12 h-1 bg-gradient-to-r from-yellow-500/50 to-transparent animate-pulse" style={{animationDelay: '1.5s'}}></div>
                  </div>
                </div>

                {/* 能量传输到下一部分 */}
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <div className="w-1 h-16 bg-gradient-to-b from-green-500/50 to-transparent animate-pulse" style={{animationDelay: '2s'}}></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full mx-auto animate-bounce"></div>
                </div>
              </div>
            </div>
          </section>

          {/* 路线图部分 - 革命性螺旋时间线布局 */}
          <section id="roadmap" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-slate-900/50">
            {/* 螺旋时间线背景 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <svg width="800" height="800" viewBox="0 0 800 800" className="opacity-20">
                <defs>
                  <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="25%" stopColor="#06B6D4" />
                    <stop offset="50%" stopColor="#10B981" />
                    <stop offset="75%" stopColor="#F59E0B" />
                    <stop offset="100%" stopColor="#EF4444" />
                  </linearGradient>
                </defs>
                {/* 螺旋时间线路径 */}
                <path
                  d="M400,400 Q500,300 600,400 Q700,500 600,600 Q500,700 400,600 Q300,500 400,400 Q450,350 500,400 Q550,450 500,500 Q450,550 400,500 Q350,450 400,400"
                  fill="none"
                  stroke="url(#timelineGradient)"
                  strokeWidth="3"
                  strokeDasharray="20,10"
                  className="animate-pulse"
                />
                {/* 时间节点 */}
                <circle cx="400" cy="400" r="8" fill="#8B5CF6" className="animate-pulse" />
                <circle cx="500" cy="300" r="8" fill="#06B6D4" className="animate-pulse" />
                <circle cx="600" cy="500" r="8" fill="#10B981" className="animate-pulse" />
                <circle cx="500" cy="700" r="8" fill="#F59E0B" className="animate-pulse" />
                <circle cx="300" cy="500" r="8" fill="#EF4444" className="animate-pulse" />
              </svg>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-green-400 bg-clip-text text-transparent">
                    发展路线图
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  以螺旋式发展路径，构建去中心化社区生态系统
                </p>
              </div>

              {/* 革命性螺旋时间线布局 */}
              <div className="relative min-h-[800px] flex items-center justify-center">

                {/* 中心起点 - 项目启动 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-full p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">启动</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">项目启动</h3>
                      <p className="text-purple-300 text-sm">2024 Q1</p>
                      <p className="text-white/70 text-xs mt-2">智能合约部署<br />社区建设启动</p>
                    </div>
                  </div>
                </div>

                {/* 第一圈 - NFT发布 */}
                <div className="absolute top-[25%] right-[20%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-blue-500/50 group-hover:border-blue-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">NFT</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">NFT发布</h3>
                      <p className="text-blue-300 text-sm">2024 Q2</p>
                      <p className="text-white/70 text-xs mt-2">10,000个独特NFT<br />分层铸造机制</p>
                    </div>
                  </div>
                </div>

                {/* 第二圈 - 社区治理 */}
                <div className="absolute bottom-[20%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-green-500/30 to-emerald-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">DAO</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">社区治理</h3>
                      <p className="text-green-300 text-sm">2024 Q3</p>
                      <p className="text-white/70 text-xs mt-2">治理机制上线<br />社区投票系统</p>
                    </div>
                  </div>
                </div>

                {/* 第三圈 - 生态扩展 */}
                <div className="absolute bottom-[25%] left-[20%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">ECO</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">生态扩展</h3>
                      <p className="text-yellow-300 text-sm">2024 Q4</p>
                      <p className="text-white/70 text-xs mt-2">合作伙伴接入<br />生态系统建设</p>
                    </div>
                  </div>
                </div>

                {/* 第四圈 - 全球化 */}
                <div className="absolute top-[20%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-red-500/50 group-hover:border-red-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-red-500/30 to-pink-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">全球</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">全球化</h3>
                      <p className="text-red-300 text-sm">2025 Q1</p>
                      <p className="text-white/70 text-xs mt-2">国际市场拓展<br />多链部署</p>
                    </div>
                  </div>
                </div>

                {/* 连接线动画 */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 800 600">
                  <path
                    d="M400,300 Q500,250 550,350 Q600,450 500,500 Q400,550 350,450 Q300,350 400,300"
                    fill="none"
                    stroke="url(#timelineGradient)"
                    strokeWidth="2"
                    strokeDasharray="8,4"
                    className="animate-pulse"
                  />
                </svg>
              </div>
            </div>
          </section>

          {/* 社区部分 - 革命性星座网络布局 */}
          <section id="community" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-slate-800/30">
            {/* 星座背景装饰 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <svg width="1000" height="800" viewBox="0 0 1000 800" className="opacity-20">
                <defs>
                  <linearGradient id="constellationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="20%" stopColor="#06B6D4" />
                    <stop offset="40%" stopColor="#10B981" />
                    <stop offset="60%" stopColor="#F59E0B" />
                    <stop offset="80%" stopColor="#EF4444" />
                    <stop offset="100%" stopColor="#EC4899" />
                  </linearGradient>
                </defs>
                {/* 星座连接线 */}
                <g stroke="url(#constellationGradient)" strokeWidth="2" strokeDasharray="5,5" className="animate-pulse">
                  <line x1="500" y1="400" x2="300" y2="200" />
                  <line x1="500" y1="400" x2="700" y2="200" />
                  <line x1="500" y1="400" x2="800" y2="400" />
                  <line x1="500" y1="400" x2="700" y2="600" />
                  <line x1="500" y1="400" x2="300" y2="600" />
                  <line x1="500" y1="400" x2="200" y2="400" />
                </g>
                {/* 星座节点 */}
                <g fill="url(#constellationGradient)">
                  <circle cx="500" cy="400" r="12" className="animate-pulse" />
                  <circle cx="300" cy="200" r="8" className="animate-pulse" />
                  <circle cx="700" cy="200" r="8" className="animate-pulse" />
                  <circle cx="800" cy="400" r="8" className="animate-pulse" />
                  <circle cx="700" cy="600" r="8" className="animate-pulse" />
                  <circle cx="300" cy="600" r="8" className="animate-pulse" />
                  <circle cx="200" cy="400" r="8" className="animate-pulse" />
                </g>
              </svg>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400 bg-clip-text text-transparent">
                    加入我们的社区
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  构建去中心化的信任网络，每个成员都是星座中闪耀的节点
                </p>
              </div>

              {/* 革命性星座网络布局 */}
              <div className="relative min-h-[700px] flex items-center justify-center">

                {/* 中心节点 - 社区核心 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-full p-12 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500">
                    <div className="absolute -inset-8 bg-gradient-to-r from-purple-500/30 via-blue-500/30 to-pink-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-20 h-20 bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span className="text-white font-bold text-2xl">🌟</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-4">AKASHA 社区</h3>
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-3xl font-bold text-purple-300">3000+</div>
                          <div className="text-white/70 text-sm">成员</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold text-blue-300">500+</div>
                          <div className="text-white/70 text-sm">NFT持有者</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold text-pink-300">50+</div>
                          <div className="text-white/70 text-sm">合作伙伴</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 上左节点 - Discord */}
                <div className="absolute top-[15%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-indigo-500/50 group-hover:border-indigo-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/30 to-purple-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">💬</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">Discord</h3>
                      <p className="text-indigo-300 text-sm">实时交流</p>
                      <p className="text-white/70 text-xs mt-2">24/7在线讨论</p>
                    </div>
                  </div>
                </div>

                {/* 上右节点 - Twitter */}
                <div className="absolute top-[15%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-sky-500/50 group-hover:border-sky-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-sky-500/30 to-blue-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🐦</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">Twitter</h3>
                      <p className="text-sky-300 text-sm">最新动态</p>
                      <p className="text-white/70 text-xs mt-2">项目更新发布</p>
                    </div>
                  </div>
                </div>

                {/* 右节点 - GitHub */}
                <div className="absolute top-1/2 right-[10%] transform -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-gray-500/50 group-hover:border-gray-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-gray-500/30 to-slate-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-gray-500 to-slate-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">⚡</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">GitHub</h3>
                      <p className="text-gray-300 text-sm">开源代码</p>
                      <p className="text-white/70 text-xs mt-2">技术透明</p>
                    </div>
                  </div>
                </div>

                {/* 下右节点 - 治理提案 */}
                <div className="absolute bottom-[15%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-emerald-500/50 group-hover:border-emerald-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-emerald-500/30 to-green-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🗳️</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">治理提案</h3>
                      <p className="text-emerald-300 text-sm">社区决策</p>
                      <p className="text-white/70 text-xs mt-2">民主投票</p>
                    </div>
                  </div>
                </div>

                {/* 下左节点 - 活动中心 */}
                <div className="absolute bottom-[15%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-orange-500/50 group-hover:border-orange-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-orange-500/30 to-red-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🎉</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">活动中心</h3>
                      <p className="text-orange-300 text-sm">社区活动</p>
                      <p className="text-white/70 text-xs mt-2">线上线下聚会</p>
                    </div>
                  </div>
                </div>

                {/* 左节点 - 学习资源 */}
                <div className="absolute top-1/2 left-[10%] transform -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/30 to-amber-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-amber-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">📚</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">学习资源</h3>
                      <p className="text-yellow-300 text-sm">知识分享</p>
                      <p className="text-white/70 text-xs mt-2">Web3教育</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* 页脚 */}
        <footer className="bg-slate-900 border-t border-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <p className="text-slate-400">
                © 2024 AKASHA DAO. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
