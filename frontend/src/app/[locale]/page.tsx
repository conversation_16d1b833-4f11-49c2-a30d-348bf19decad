'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { useTranslation } from '@/components/providers/I18nProvider';
import { WalletButton } from '@/components/wallet/WalletButton';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { PRICES } from '@/lib/web3';
import { Logo } from '@/components/ui/Logo';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';

export default function Home() {
  const { address, isConnected } = useAccount();
  const { t, tArray } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % 3);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <div className="min-h-screen bg-black overflow-hidden relative">
        {/* 动态背景 */}
        <div className="fixed inset-0 z-0">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-60"
          style={{
            backgroundImage: 'url(/images/web3-hero-background.webp)',
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-purple-900/50 to-black/80" />

        {/* 动态粒子效果 */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-purple-400 rounded-full opacity-30 animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`,
              }}
            />
          ))}
        </div>
      </div>

      {/* 导航栏 */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <Logo size="lg" showText={true} showSubtitle={true} href="/" />

            {/* 导航菜单 */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#about" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.about')}</a>
              <a href="#tiers" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">分层体系</a>
              <a href="#benefits" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">权益</a>
              <a href="#roadmap" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.roadmap')}</a>
              <a href="#community" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.community')}</a>
            </div>

            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              <a
                href="/mint"
                className="px-8 py-3 bg-gradient-to-r from-purple-600 via-purple-500 to-blue-600 hover:from-purple-700 hover:via-purple-600 hover:to-blue-700 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/25 border border-purple-400/30"
              >
                {t('nav.mint')}
              </a>
              <WalletButton />
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 - 革命性螺旋布局 */}
      <main className="relative z-10">
        {/* Hero 区域 - 螺旋式创新布局 */}
        <section className="min-h-screen relative overflow-hidden pt-20">
          {/* 螺旋路径容器 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative w-full h-full max-w-6xl">

              {/* 中心核心 - AKASHA DAO */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
                <div className="relative group">
                  {/* 中心发光核心 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-purple-600/40 via-cyan-600/40 to-pink-600/40 blur-3xl animate-pulse"></div>
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-full p-12 border border-white/30 group-hover:border-white/50 transition-all duration-500">
                    <div className="text-center">
                      <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
                        <span className="bg-gradient-to-r from-purple-300 via-cyan-300 to-pink-300 bg-clip-text text-transparent">
                          AKASHA
                        </span>
                        <br />
                        <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent">
                          DAO
                        </span>
                      </h1>
                      <p className="text-white/80 text-lg mb-6">神秘组织 · 信任象征</p>
                      <a
                        href="/mint"
                        className="inline-block px-8 py-4 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 rounded-full font-bold text-white transition-all duration-500 hover:scale-110 shadow-2xl shadow-purple-500/50"
                      >
                        立即铸造
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              {/* 螺旋路径上的元素 */}
              {/* 第一象限 - NFT特性 */}
              <div className="absolute top-[20%] right-[15%] transform rotate-12 group">
                <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-purple-500/30 group-hover:border-purple-500/60 transition-all duration-500 group-hover:scale-105">
                  <div className="absolute -inset-2 bg-gradient-to-r from-purple-500/20 to-blue-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-white font-bold text-xl">NFT</span>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">独特身份</h3>
                    <p className="text-white/70 text-sm">10,000个独特NFT<br />专属社区通行证</p>
                  </div>
                </div>
              </div>

              {/* 第二象限 - 治理权益 */}
              <div className="absolute top-[15%] left-[20%] transform -rotate-12 group">
                <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-cyan-500/30 group-hover:border-cyan-500/60 transition-all duration-500 group-hover:scale-105">
                  <div className="absolute -inset-2 bg-gradient-to-r from-cyan-500/20 to-teal-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-white font-bold text-xl">DAO</span>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">治理权益</h3>
                    <p className="text-white/70 text-sm">参与决策投票<br />共建社区未来</p>
                  </div>
                </div>
              </div>

              {/* 第三象限 - 分层体系 */}
              <div className="absolute bottom-[25%] left-[10%] transform rotate-12 group">
                <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-pink-500/30 group-hover:border-pink-500/60 transition-all duration-500 group-hover:scale-105">
                  <div className="absolute -inset-2 bg-gradient-to-r from-pink-500/20 to-red-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-white font-bold text-xl">TIER</span>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">分层体系</h3>
                    <p className="text-white/70 text-sm">白名单 / 学生<br />差异化权益</p>
                  </div>
                </div>
              </div>

              {/* 第四象限 - Web3生态 */}
              <div className="absolute bottom-[20%] right-[25%] transform -rotate-12 group">
                <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-yellow-500/30 group-hover:border-yellow-500/60 transition-all duration-500 group-hover:scale-105">
                  <div className="absolute -inset-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-white font-bold text-xl">WEB3</span>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">Web3生态</h3>
                    <p className="text-white/70 text-sm">链上身份认证<br />去中心化治理</p>
                  </div>
                </div>
              </div>

              {/* 连接线和动画轨道 */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 800 600">
                {/* 螺旋连接线 */}
                <path
                  d="M400,300 Q500,200 600,250 Q650,350 550,400 Q350,450 250,350 Q200,250 300,200"
                  fill="none"
                  stroke="url(#spiralGradient)"
                  strokeWidth="2"
                  strokeDasharray="10,5"
                  className="animate-pulse"
                />
                <defs>
                  <linearGradient id="spiralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.5" />
                    <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.5" />
                    <stop offset="100%" stopColor="#ec4899" stopOpacity="0.5" />
                  </linearGradient>
                </defs>
              </svg>

              {/* 数据统计浮动元素 */}
              <div className="absolute top-[35%] right-[5%] group">
                <div className="relative p-6 bg-black/30 backdrop-blur-xl rounded-2xl border border-white/20 transform rotate-6 group-hover:rotate-0 transition-all duration-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">3,000+</div>
                    <div className="text-white/60 text-sm">社区成员</div>
                  </div>
                </div>
              </div>

              <div className="absolute bottom-[35%] left-[5%] group">
                <div className="relative p-6 bg-black/30 backdrop-blur-xl rounded-2xl border border-white/20 transform -rotate-6 group-hover:rotate-0 transition-all duration-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">500+</div>
                    <div className="text-white/60 text-sm">NFT持有者</div>
                  </div>
                </div>
              </div>

              <div className="absolute top-[10%] left-[45%] group">
                <div className="relative p-6 bg-black/30 backdrop-blur-xl rounded-2xl border border-white/20 transform rotate-3 group-hover:rotate-0 transition-all duration-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">LIVE</div>
                    <div className="text-green-400 text-sm flex items-center justify-center">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                      正在铸造
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute bottom-[10%] right-[45%] group">
                <div className="relative p-6 bg-black/30 backdrop-blur-xl rounded-2xl border border-white/20 transform -rotate-3 group-hover:rotate-0 transition-all duration-500">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white mb-1">0.08 ETH</div>
                    <div className="text-white/60 text-sm">铸造价格</div>
                  </div>
                </div>
              </div>

              {/* 神秘人物背景装饰 */}
              <div className="absolute top-[60%] left-[70%] opacity-20 pointer-events-none">
                <div
                  className="w-48 h-64 bg-cover bg-center bg-no-repeat"
                  style={{
                    backgroundImage: 'url(/images/akasha-mysterious-figure.webp)',
                  }}
                ></div>
              </div>

              {/* 旋转装饰环 */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
                <div className="w-[120vmin] h-[120vmin] border border-white/5 rounded-full animate-spin" style={{animationDuration: '120s'}}></div>
                <div className="absolute inset-8 border border-white/10 rounded-full animate-spin" style={{animationDuration: '80s', animationDirection: 'reverse'}}></div>
                <div className="absolute inset-16 border border-white/5 rounded-full animate-spin" style={{animationDuration: '100s'}}></div>
              </div>
            </div>
          </div>
        </section>

        {/* 分层体系区域 */}
        <section id="tiers" className="py-32 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  分层体系
                </span>
              </h2>
              <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                基于信任构建的精英社区，为不同阶段的创业者和投资人提供精准匹配的价值服务
              </p>
            </div>

            {/* 当前阶段 */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <div className="inline-flex items-center px-6 py-3 bg-purple-500/20 border border-purple-500/30 rounded-full text-purple-300 text-lg font-medium mb-4">
                  🚀 第一阶段 - 现已开放
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
                {/* 白名单层级 */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-xl rounded-3xl p-8 border border-yellow-500/30 hover:border-yellow-400/50 transition-all duration-300">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                        <span className="text-white font-bold text-xl">👑</span>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">白名单</h3>
                        <p className="text-yellow-400 font-medium">创世元老 · 100人</p>
                      </div>
                    </div>

                    <div className="space-y-4 mb-8">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-slate-300">免费无限阅读Akasha paragraph文章</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-slate-300">核心创始团队和治理者身份</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-slate-300">提出治理意见的权利</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-slate-300">NFT专属标识，享有最高阶权益</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="text-slate-300">Mint优惠价格</span>
                      </div>
                    </div>

                    <div className="border-t border-slate-700 pt-6">
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-slate-400">价格</span>
                        <span className="text-2xl font-bold text-white">0.08 ETH</span>
                      </div>
                      <div className="flex justify-between items-center mb-6">
                        <span className="text-slate-400">分发方式</span>
                        <span className="text-yellow-400 font-medium">80% 白名单 + 20% 抽奖</span>
                      </div>
                      <a
                        href="/mint"
                        className="w-full block text-center px-8 py-4 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold rounded-2xl transition-all duration-300 transform hover:scale-105"
                      >
                        立即铸造
                      </a>
                    </div>
                  </div>
                </div>

                {/* 底层（大学生）层级 */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div className="relative bg-gradient-to-br from-slate-900/90 to-slate-800/90 backdrop-blur-xl rounded-3xl p-8 border border-blue-500/30 hover:border-blue-400/50 transition-all duration-300">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                        <span className="text-white font-bold text-xl">🎓</span>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">底层</h3>
                        <p className="text-blue-400 font-medium">大学生群体</p>
                      </div>
                    </div>

                    <div className="space-y-4 mb-8">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-slate-300">免费无限阅读Akasha paragraph文章</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-slate-300">参与985、藤校优秀在校生私享会</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-slate-300">优质项目方创业者分享会</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-slate-300">帮助链接普通VC资源</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-slate-300">通俗方法论内容输出</span>
                      </div>
                    </div>

                    <div className="border-t border-slate-700 pt-6">
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-slate-400">价格</span>
                        <span className="text-2xl font-bold text-white">0.12 ETH</span>
                      </div>
                      <div className="flex justify-between items-center mb-6">
                        <span className="text-slate-400">目标群体</span>
                        <span className="text-blue-400 font-medium">在校大学生</span>
                      </div>
                      <a
                        href="/mint"
                        className="w-full block text-center px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 transform hover:scale-105"
                      >
                        立即铸造
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 未来阶段预告 */}
            <div className="text-center">
              <div className="inline-flex items-center px-6 py-3 bg-slate-700/50 border border-slate-600/50 rounded-full text-slate-400 text-lg font-medium mb-8">
                🔮 未来阶段 - 敬请期待
              </div>

              <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                {/* 中阶 */}
                <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30">
                  <div className="text-3xl mb-4">🚀</div>
                  <h4 className="text-xl font-bold text-white mb-2">中阶</h4>
                  <p className="text-slate-400 text-sm">初期创业者</p>
                  <p className="text-slate-500 text-xs mt-2">思维和方法论提升</p>
                </div>

                {/* 中高阶 */}
                <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30">
                  <div className="text-3xl mb-4">💎</div>
                  <h4 className="text-xl font-bold text-white mb-2">中高阶</h4>
                  <p className="text-slate-400 text-sm">有成就的创业者</p>
                  <p className="text-slate-500 text-xs mt-2">高级认知和资源对接</p>
                </div>

                {/* 高阶 */}
                <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30">
                  <div className="text-3xl mb-4">👑</div>
                  <h4 className="text-xl font-bold text-white mb-2">高阶</h4>
                  <p className="text-slate-400 text-sm">交易所/VC老板</p>
                  <p className="text-slate-500 text-xs mt-2">币圈知名人士专属</p>
                </div>
              </div>
            </div>
          </div>
        </section>

      {/* 特性展示 */}
      {/* 核心价值 */}
      <section id="about" className="py-32 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
              <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                核心价值
              </span>
            </h2>
            <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
              AkashaDao不仅是NFT，更是进入精英社区的通行证，基于信任构建的价值网络
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            {/* 神秘IP */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-gradient-to-br from-slate-900/80 to-slate-800/80 backdrop-blur-xl rounded-3xl p-8 border border-purple-500/20 hover:border-purple-400/40 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                  <span className="text-2xl">🎭</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">神秘IP</h3>
                <p className="text-slate-300 leading-relaxed">
                  维持神秘人设，让信任成为唯一的价值锚点。不依赖个人品牌，而是构建基于共识的价值体系。
                </p>
              </div>
            </div>

            {/* 信任机制 */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-gradient-to-br from-slate-900/80 to-slate-800/80 backdrop-blur-xl rounded-3xl p-8 border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                  <span className="text-2xl">🤝</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">信任为本</h3>
                <p className="text-slate-300 leading-relaxed">
                  为NFT holder买单的本质是"信任"。我们通过持续的价值输出和社区建设来维护这份信任。
                </p>
              </div>
            </div>

            {/* 价值共创 */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-gradient-to-br from-slate-900/80 to-slate-800/80 backdrop-blur-xl rounded-3xl p-8 border border-emerald-500/20 hover:border-emerald-400/40 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                  <span className="text-2xl">💎</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">价值共创</h3>
                <p className="text-slate-300 leading-relaxed">
                  通过分层权益体系，为不同阶段的成员提供精准的价值服务，实现社区价值的持续增长。
                </p>
              </div>
            </div>
          </div>

          {/* 我们能提供什么 */}
          <div className="mt-32">
            <div className="text-center mb-16">
              <h3 className="text-4xl font-bold text-white mb-6">我们能为NFT Holder提供什么？</h3>
              <p className="text-lg text-slate-400 max-w-2xl mx-auto">
                参考本末社区的成功运营模式，为不同层级的成员提供差异化的价值服务
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* 独家内容 */}
              <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30 hover:border-purple-500/30 transition-all duration-300">
                <div className="text-3xl mb-4">📚</div>
                <h4 className="text-lg font-bold text-white mb-3">独家内容</h4>
                <p className="text-slate-400 text-sm">Akasha paragraph文章，深度思考和方法论分享</p>
              </div>

              {/* 私享会 */}
              <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30 hover:border-blue-500/30 transition-all duration-300">
                <div className="text-3xl mb-4">🎯</div>
                <h4 className="text-lg font-bold text-white mb-3">私享会</h4>
                <p className="text-slate-400 text-sm">985、藤校优秀在校生和创业者分享会</p>
              </div>

              {/* 资源对接 */}
              <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30 hover:border-emerald-500/30 transition-all duration-300">
                <div className="text-3xl mb-4">🔗</div>
                <h4 className="text-lg font-bold text-white mb-3">资源对接</h4>
                <p className="text-slate-400 text-sm">帮助创业者链接VC资源和合作伙伴</p>
              </div>

              {/* 治理权益 */}
              <div className="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-6 border border-slate-700/30 hover:border-yellow-500/30 transition-all duration-300">
                <div className="text-3xl mb-4">⚖️</div>
                <h4 className="text-lg font-bold text-white mb-3">治理权益</h4>
                <p className="text-slate-400 text-sm">参与社区治理，享有决策权和提案权</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 路线图部分 */}
      <section id="roadmap" className="py-20 bg-slate-900/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('roadmap.title')}
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              {t('roadmap.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Q1 2024 */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700 hover:border-purple-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4">
                <span className="text-white font-bold">Q1</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t('roadmap.q1.title')}</h3>
              <ul className="space-y-2">
                {tArray('roadmap.q1.items').map((item: string, index: number) => (
                  <li key={index} className="flex items-start text-slate-300">
                    <svg className="w-5 h-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            {/* Q2 2024 */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700 hover:border-purple-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
                <span className="text-white font-bold">Q2</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t('roadmap.q2.title')}</h3>
              <ul className="space-y-2">
                {tArray('roadmap.q2.items').map((item: string, index: number) => (
                  <li key={index} className="flex items-start text-slate-300">
                    <svg className="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            {/* Q3 2024 */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700 hover:border-purple-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-4">
                <span className="text-white font-bold">Q3</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t('roadmap.q3.title')}</h3>
              <ul className="space-y-2">
                {tArray('roadmap.q3.items').map((item: string, index: number) => (
                  <li key={index} className="flex items-start text-slate-300">
                    <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    {item}
                  </li>
                ))}
              </ul>
            </div>

            {/* Q4 2024 */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-700 hover:border-purple-500/50 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4">
                <span className="text-white font-bold">Q4</span>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{t('roadmap.q4.title')}</h3>
              <ul className="space-y-2">
                {tArray('roadmap.q4.items').map((item: string, index: number) => (
                  <li key={index} className="flex items-start text-slate-300">
                    <svg className="w-5 h-5 text-orange-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 社区部分 */}
      <section id="community" className="py-20 bg-slate-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('community.title')}
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              {t('community.subtitle')}
            </p>
          </div>

          {/* 社区统计数据 */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="text-center bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="text-4xl font-bold text-white mb-2">3000+</div>
              <div className="text-slate-300">{t('community.members')}</div>
            </div>

            <div className="text-center bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div className="text-4xl font-bold text-white mb-2">500+</div>
              <div className="text-slate-300">{t('community.nftHolders')}</div>
            </div>

            <div className="text-center bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700">
              <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                </svg>
              </div>
              <div className="text-4xl font-bold text-white mb-2">50+</div>
              <div className="text-slate-300">{t('community.partners')}</div>
            </div>
          </div>

          {/* 社区活动 */}
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700">
              <h3 className="text-2xl font-bold text-white mb-4">{t('community.events')}</h3>
              <p className="text-slate-300 mb-6">定期举办线上线下活动，促进社区成员交流与合作</p>
              <div className="space-y-3">
                <div className="flex items-center text-slate-300">
                  <svg className="w-5 h-5 text-purple-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  每月社区AMA
                </div>
                <div className="flex items-center text-slate-300">
                  <svg className="w-5 h-5 text-purple-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  技术分享会
                </div>
                <div className="flex items-center text-slate-300">
                  <svg className="w-5 h-5 text-purple-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  创业者聚会
                </div>
              </div>
            </div>

            <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700">
              <h3 className="text-2xl font-bold text-white mb-4">{t('community.proposals')}</h3>
              <p className="text-slate-300 mb-6">参与社区治理，提出和投票决定社区发展方向</p>
              <div className="space-y-3">
                <div className="flex items-center text-slate-300">
                  <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  社区发展提案
                </div>
                <div className="flex items-center text-slate-300">
                  <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  资金分配投票
                </div>
                <div className="flex items-center text-slate-300">
                  <svg className="w-5 h-5 text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  合作伙伴选择
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      </main>

      {/* 页脚 */}
      <footer className="bg-slate-900 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Logo和描述 */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">A</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">AkashaDao</h3>
                  <span className="text-sm text-purple-300">Community Pass</span>
                </div>
              </div>
              <p className="text-slate-400 max-w-md mb-6">
                {t('footer.description')}
              </p>
            </div>
          </div>

          <div className="border-t border-slate-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">
              &copy; 2025 AkashaDao. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
      </div>
    </>
  );
}
