'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { useTranslation } from '@/components/providers/I18nProvider';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Logo } from '@/components/ui/Logo';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';

export default function Home() {
  const { address, isConnected } = useAccount();
  const { t, tArray } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 动态背景效果
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.zIndex = '1';
    canvas.style.pointerEvents = 'none';
    document.body.appendChild(canvas);

    return () => {
      if (document.body.contains(canvas)) {
        document.body.removeChild(canvas);
      }
    };
  }, []);

  return (
    <>
      <div className="min-h-screen bg-black overflow-hidden relative">
        {/* 动态背景 */}
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
        </div>

        {/* 导航栏 */}
        <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-20">
              <Logo size="lg" showText={true} showSubtitle={true} href="/" />

              {/* 导航菜单 */}
              <div className="hidden md:flex items-center space-x-8">
                <a href="#about" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.about')}</a>
                <a href="#tiers" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">分层体系</a>
                <a href="#benefits" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">权益</a>
                <a href="#roadmap" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.roadmap')}</a>
                <a href="#community" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.community')}</a>
              </div>

              <div className="flex items-center space-x-4">
                <LanguageSwitcher />
                <a
                  href="/mint"
                  className="px-8 py-3 bg-gradient-to-r from-purple-600 via-purple-500 to-blue-600 hover:from-purple-700 hover:via-purple-600 hover:to-blue-700 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/25 border border-purple-400/30"
                >
                  {t('nav.mint')}
                </a>
                <WalletButton />
              </div>
            </div>
          </div>
        </nav>

        {/* 主要内容 */}
        <main className="relative z-10">
          {/* Hero 区域 - 螺旋式创新布局 */}
          <section className="min-h-screen relative overflow-hidden pt-20">
            {/* 螺旋路径容器 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-full h-full max-w-6xl">

                {/* 中心核心 - AKASHA DAO */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
                  <div className="relative group">
                    {/* 中心发光核心 */}
                    <div className="absolute -inset-8 bg-gradient-to-r from-purple-600/40 via-cyan-600/40 to-pink-600/40 blur-3xl animate-pulse"></div>
                    <div className="relative bg-black/60 backdrop-blur-2xl rounded-full p-12 border border-white/30 group-hover:border-white/50 transition-all duration-500">
                      <div className="text-center">
                        <h1 className="text-4xl md:text-6xl font-black text-white mb-4">
                          <span className="bg-gradient-to-r from-purple-300 via-cyan-300 to-pink-300 bg-clip-text text-transparent">
                            AKASHA
                          </span>
                          <br />
                          <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent">
                            DAO
                          </span>
                        </h1>
                        <p className="text-white/80 text-lg mb-6">神秘组织 · 信任象征</p>
                        <a
                          href="/mint"
                          className="inline-block px-8 py-4 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 rounded-full font-bold text-white transition-all duration-500 hover:scale-110 shadow-2xl shadow-purple-500/50"
                        >
                          立即铸造
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 螺旋路径上的元素 */}
                {/* 第一象限 - NFT特性 */}
                <div className="absolute top-[20%] right-[15%] transform rotate-12 group">
                  <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-purple-500/30 group-hover:border-purple-500/60 transition-all duration-500 group-hover:scale-105">
                    <div className="absolute -inset-2 bg-gradient-to-r from-purple-500/20 to-blue-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">NFT</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">独特身份</h3>
                      <p className="text-white/70 text-sm">10,000个独特NFT<br />专属社区通行证</p>
                    </div>
                  </div>
                </div>

                {/* 第二象限 - 治理权益 */}
                <div className="absolute top-[15%] left-[20%] transform -rotate-12 group">
                  <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-cyan-500/30 group-hover:border-cyan-500/60 transition-all duration-500 group-hover:scale-105">
                    <div className="absolute -inset-2 bg-gradient-to-r from-cyan-500/20 to-teal-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">DAO</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">治理权益</h3>
                      <p className="text-white/70 text-sm">参与决策投票<br />共建社区未来</p>
                    </div>
                  </div>
                </div>

                {/* 第三象限 - 分层体系 */}
                <div className="absolute bottom-[20%] left-[15%] transform rotate-12 group">
                  <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-pink-500/30 group-hover:border-pink-500/60 transition-all duration-500 group-hover:scale-105">
                    <div className="absolute -inset-2 bg-gradient-to-r from-pink-500/20 to-rose-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">TIER</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">分层体系</h3>
                      <p className="text-white/70 text-sm">白名单/学生层级<br />精英社区准入</p>
                    </div>
                  </div>
                </div>

                {/* 第四象限 - 社区生态 */}
                <div className="absolute bottom-[15%] right-[20%] transform -rotate-12 group">
                  <div className="relative p-8 bg-black/40 backdrop-blur-xl rounded-3xl border border-yellow-500/30 group-hover:border-yellow-500/60 transition-all duration-500 group-hover:scale-105">
                    <div className="absolute -inset-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">ECO</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">生态系统</h3>
                      <p className="text-white/70 text-sm">3000+成员<br />全球化社区</p>
                    </div>
                  </div>
                </div>

                {/* 连接线动画 */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 800 600">
                  <path
                    d="M400,300 Q500,200 600,250 Q650,350 550,400 Q450,450 350,400 Q250,350 300,250 Q350,200 400,300"
                    fill="none"
                    stroke="url(#spiralGradient)"
                    strokeWidth="2"
                    strokeDasharray="10,5"
                    className="animate-pulse"
                  />
                  <defs>
                    <linearGradient id="spiralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.6" />
                      <stop offset="25%" stopColor="#06b6d4" stopOpacity="0.6" />
                      <stop offset="50%" stopColor="#ec4899" stopOpacity="0.6" />
                      <stop offset="75%" stopColor="#f59e0b" stopOpacity="0.6" />
                      <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.6" />
                    </linearGradient>
                  </defs>
                </svg>

                {/* 旋转光环 */}
                <div className="absolute inset-8 border border-white/10 rounded-full animate-spin" style={{animationDuration: '20s'}}></div>
                <div className="absolute inset-12 border border-white/5 rounded-full animate-spin" style={{animationDuration: '30s'}}></div>
                <div className="absolute inset-16 border border-white/5 rounded-full animate-spin" style={{animationDuration: '40s'}}></div>
              </div>
            </div>
          </section>

          {/* 分层体系区域 - 革命性金字塔布局 */}
          <section id="tiers" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            {/* 金字塔背景装饰 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="relative">
                {/* 大型金字塔轮廓 */}
                <svg width="800" height="600" viewBox="0 0 800 600" className="opacity-10">
                  <defs>
                    <linearGradient id="pyramidGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#8B5CF6" />
                      <stop offset="50%" stopColor="#06B6D4" />
                      <stop offset="100%" stopColor="#EC4899" />
                    </linearGradient>
                  </defs>
                  <polygon
                    points="400,50 150,550 650,550"
                    fill="none"
                    stroke="url(#pyramidGradient)"
                    strokeWidth="2"
                    strokeDasharray="10,5"
                    className="animate-pulse"
                  />
                  {/* 内部分层线 */}
                  <line x1="250" y1="350" x2="550" y2="350" stroke="url(#pyramidGradient)" strokeWidth="1" strokeDasharray="5,5" className="animate-pulse" />
                  <line x1="300" y1="450" x2="500" y2="450" stroke="url(#pyramidGradient)" strokeWidth="1" strokeDasharray="5,5" className="animate-pulse" />
                </svg>
              </div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                    分层体系
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  基于信任构建的精英社区，为不同阶段的创业者和投资人提供精准匹配的价值服务
                </p>
              </div>

              {/* 革命性金字塔层级布局 */}
              <div className="relative min-h-[800px] flex items-center justify-center">

                {/* 顶层 - 白名单层 (金字塔顶端) */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500"
                    style={{
                      width: '300px',
                      height: '200px',
                      clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/30 via-blue-500/30 to-cyan-500/30 blur-2xl opacity-50 group-hover:opacity-70 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-6 text-center">
                      <div className="mb-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center mb-3">
                          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M9.664 1.319a.75.75 0 01.672 0 41.059 41.059 0 018.198 5.424.75.75 0 01-.254 1.285 31.372 31.372 0 00-7.86 ********** 0 01-.84 0 31.508 31.508 0 00-2.08-1.287V9.394c0-.244.116-.463.302-.592a35.504 35.504 0 013.305-*********** 0 00-.714-1.319 37 37 0 00-3.446 2.12A2.216 2.216 0 006 9.393v.38a31.293 31.293 0 00-4.28-1.746.75.75 0 01-.254-1.285 41.059 41.059 0 018.198-5.424zM6 11.459a29.848 29.848 0 00-2.455-1.158 41.029 41.029 0 00-.39 *********** 0 00.419.74c.528.256 1.046.53 1.554.82-.21-.899-.455-1.746-.721-2.517a.75.75 0 00-.407-.999zm2.353 2.542a.75.75 0 01.407-.999 29.67 29.67 0 011.721-2.517c.266.771.511 1.618.721 2.517a.75.75 0 01-.407.999 29.67 29.67 0 01-1.721 2.517c-.266-.771-.511-1.618-.721-2.517z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">白名单层</h3>
                        <p className="text-purple-300 text-sm">精英投资人</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">0.08 ETH</div>
                        <div className="text-purple-300 text-xs">铸造价格</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 中层 - 学生层 (金字塔中部) */}
                <div className="absolute top-48 left-1/2 transform -translate-x-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-cyan-500/50 group-hover:border-cyan-500/80 transition-all duration-500"
                    style={{
                      width: '400px',
                      height: '250px',
                      clipPath: 'polygon(25% 0%, 75% 0%, 100% 100%, 0% 100%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500/30 via-blue-500/30 to-purple-500/30 blur-2xl opacity-50 group-hover:opacity-70 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-8 text-center">
                      <div className="mb-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center mb-3">
                          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                          </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">学生层</h3>
                        <p className="text-cyan-300 text-sm">创业者社区</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">0.05 ETH</div>
                        <div className="text-cyan-300 text-xs">铸造价格</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 底层 - 公开层 (金字塔底部) */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-pink-500/50 group-hover:border-pink-500/80 transition-all duration-500"
                    style={{
                      width: '500px',
                      height: '200px',
                      clipPath: 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-pink-500/30 via-purple-500/30 to-blue-500/30 blur-2xl opacity-50 group-hover:opacity-70 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-8 text-center">
                      <div className="mb-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full flex items-center justify-center mb-3">
                          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                          </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">公开层</h3>
                        <p className="text-pink-300 text-sm">社区成员</p>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">0.03 ETH</div>
                        <div className="text-pink-300 text-xs">铸造价格</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 核心价值 - 革命性钻石布局 */}
          <section id="about" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            {/* 钻石背景装饰 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="relative">
                {/* 大型钻石轮廓 */}
                <svg width="600" height="600" viewBox="0 0 600 600" className="opacity-10">
                  <defs>
                    <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#8B5CF6" />
                      <stop offset="33%" stopColor="#06B6D4" />
                      <stop offset="66%" stopColor="#EC4899" />
                      <stop offset="100%" stopColor="#F59E0B" />
                    </linearGradient>
                  </defs>
                  <polygon
                    points="300,50 450,200 300,550 150,200"
                    fill="none"
                    stroke="url(#diamondGradient)"
                    strokeWidth="3"
                    strokeDasharray="15,10"
                    className="animate-pulse"
                  />
                  {/* 内部钻石切面 */}
                  <polygon points="300,50 375,150 300,300 225,150" fill="none" stroke="url(#diamondGradient)" strokeWidth="1" strokeDasharray="8,4" className="animate-pulse" />
                  <polygon points="300,300 450,200 300,550 150,200" fill="none" stroke="url(#diamondGradient)" strokeWidth="1" strokeDasharray="8,4" className="animate-pulse" />
                </svg>
              </div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                    核心价值
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  AkashaDao不仅是NFT，更是进入精英社区的通行证，基于信任构建的价值网络
                </p>
              </div>

              {/* 革命性钻石价值布局 */}
              <div className="relative min-h-[700px] flex items-center justify-center">

                {/* 顶部钻石 - 神秘IP */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500"
                    style={{
                      width: '280px',
                      height: '200px',
                      clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-6 bg-gradient-to-r from-purple-500/30 via-pink-500/30 to-purple-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-6 text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <span className="text-2xl">🎭</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">神秘IP</h3>
                      <p className="text-purple-300 text-xs leading-tight">维持神秘人设<br />信任为锚点</p>
                    </div>
                  </div>
                </div>

                {/* 左侧钻石 - 信任机制 */}
                <div className="absolute left-8 top-1/2 transform -translate-y-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-blue-500/50 group-hover:border-blue-500/80 transition-all duration-500"
                    style={{
                      width: '280px',
                      height: '200px',
                      clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-6 bg-gradient-to-r from-blue-500/30 via-cyan-500/30 to-blue-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-6 text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <span className="text-2xl">🤝</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">信任为本</h3>
                      <p className="text-blue-300 text-xs leading-tight">持续价值输出<br />维护社区信任</p>
                    </div>
                  </div>
                </div>

                {/* 右侧钻石 - 价值共创 */}
                <div className="absolute right-8 top-1/2 transform -translate-y-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-emerald-500/50 group-hover:border-emerald-500/80 transition-all duration-500"
                    style={{
                      width: '280px',
                      height: '200px',
                      clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-6 bg-gradient-to-r from-emerald-500/30 via-green-500/30 to-emerald-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-6 text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <span className="text-2xl">💎</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">价值共创</h3>
                      <p className="text-emerald-300 text-xs leading-tight">社区共建<br />价值共享</p>
                    </div>
                  </div>
                </div>

                {/* 底部钻石 - 生态发展 */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 group">
                  <div
                    className="relative bg-black/60 backdrop-blur-2xl border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500"
                    style={{
                      width: '280px',
                      height: '200px',
                      clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
                    }}
                  >
                    {/* 发光效果 */}
                    <div className="absolute -inset-6 bg-gradient-to-r from-yellow-500/30 via-orange-500/30 to-yellow-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>

                    <div className="relative h-full flex flex-col items-center justify-center p-6 text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                        <span className="text-2xl">🌟</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">生态发展</h3>
                      <p className="text-yellow-300 text-xs leading-tight">持续创新<br />生态繁荣</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 路线图部分 - 革命性螺旋时间线布局 */}
          <section id="roadmap" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-slate-900/50">
            {/* 螺旋时间线背景 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <svg width="800" height="800" viewBox="0 0 800 800" className="opacity-20">
                <defs>
                  <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="25%" stopColor="#06B6D4" />
                    <stop offset="50%" stopColor="#10B981" />
                    <stop offset="75%" stopColor="#F59E0B" />
                    <stop offset="100%" stopColor="#EF4444" />
                  </linearGradient>
                </defs>
                {/* 螺旋时间线路径 */}
                <path
                  d="M400,400 Q500,300 600,400 Q700,500 600,600 Q500,700 400,600 Q300,500 400,400 Q450,350 500,400 Q550,450 500,500 Q450,550 400,500 Q350,450 400,400"
                  fill="none"
                  stroke="url(#timelineGradient)"
                  strokeWidth="3"
                  strokeDasharray="20,10"
                  className="animate-pulse"
                />
                {/* 时间节点 */}
                <circle cx="400" cy="400" r="8" fill="#8B5CF6" className="animate-pulse" />
                <circle cx="500" cy="300" r="8" fill="#06B6D4" className="animate-pulse" />
                <circle cx="600" cy="500" r="8" fill="#10B981" className="animate-pulse" />
                <circle cx="500" cy="700" r="8" fill="#F59E0B" className="animate-pulse" />
                <circle cx="300" cy="500" r="8" fill="#EF4444" className="animate-pulse" />
              </svg>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-green-400 bg-clip-text text-transparent">
                    发展路线图
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  以螺旋式发展路径，构建去中心化社区生态系统
                </p>
              </div>

              {/* 革命性螺旋时间线布局 */}
              <div className="relative min-h-[800px] flex items-center justify-center">

                {/* 中心起点 - 项目启动 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-full p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">启动</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">项目启动</h3>
                      <p className="text-purple-300 text-sm">2024 Q1</p>
                      <p className="text-white/70 text-xs mt-2">智能合约部署<br />社区建设启动</p>
                    </div>
                  </div>
                </div>

                {/* 第一圈 - NFT发布 */}
                <div className="absolute top-[25%] right-[20%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-blue-500/50 group-hover:border-blue-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">NFT</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">NFT发布</h3>
                      <p className="text-blue-300 text-sm">2024 Q2</p>
                      <p className="text-white/70 text-xs mt-2">10,000个独特NFT<br />分层铸造机制</p>
                    </div>
                  </div>
                </div>

                {/* 第二圈 - 社区治理 */}
                <div className="absolute bottom-[20%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-green-500/30 to-emerald-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">DAO</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">社区治理</h3>
                      <p className="text-green-300 text-sm">2024 Q3</p>
                      <p className="text-white/70 text-xs mt-2">治理机制上线<br />社区投票系统</p>
                    </div>
                  </div>
                </div>

                {/* 第三圈 - 生态扩展 */}
                <div className="absolute bottom-[25%] left-[20%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">ECO</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">生态扩展</h3>
                      <p className="text-yellow-300 text-sm">2024 Q4</p>
                      <p className="text-white/70 text-xs mt-2">合作伙伴接入<br />生态系统建设</p>
                    </div>
                  </div>
                </div>

                {/* 第四圈 - 全球化 */}
                <div className="absolute top-[20%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-red-500/50 group-hover:border-red-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-red-500/30 to-pink-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">全球</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">全球化</h3>
                      <p className="text-red-300 text-sm">2025 Q1</p>
                      <p className="text-white/70 text-xs mt-2">国际市场拓展<br />多链部署</p>
                    </div>
                  </div>
                </div>

                {/* 连接线动画 */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 800 600">
                  <path
                    d="M400,300 Q500,250 550,350 Q600,450 500,500 Q400,550 350,450 Q300,350 400,300"
                    fill="none"
                    stroke="url(#timelineGradient)"
                    strokeWidth="2"
                    strokeDasharray="8,4"
                    className="animate-pulse"
                  />
                </svg>
              </div>
            </div>
          </section>

          {/* 社区部分 - 革命性星座网络布局 */}
          <section id="community" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-slate-800/30">
            {/* 星座背景装饰 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <svg width="1000" height="800" viewBox="0 0 1000 800" className="opacity-20">
                <defs>
                  <linearGradient id="constellationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="20%" stopColor="#06B6D4" />
                    <stop offset="40%" stopColor="#10B981" />
                    <stop offset="60%" stopColor="#F59E0B" />
                    <stop offset="80%" stopColor="#EF4444" />
                    <stop offset="100%" stopColor="#EC4899" />
                  </linearGradient>
                </defs>
                {/* 星座连接线 */}
                <g stroke="url(#constellationGradient)" strokeWidth="2" strokeDasharray="5,5" className="animate-pulse">
                  <line x1="500" y1="400" x2="300" y2="200" />
                  <line x1="500" y1="400" x2="700" y2="200" />
                  <line x1="500" y1="400" x2="800" y2="400" />
                  <line x1="500" y1="400" x2="700" y2="600" />
                  <line x1="500" y1="400" x2="300" y2="600" />
                  <line x1="500" y1="400" x2="200" y2="400" />
                </g>
                {/* 星座节点 */}
                <g fill="url(#constellationGradient)">
                  <circle cx="500" cy="400" r="12" className="animate-pulse" />
                  <circle cx="300" cy="200" r="8" className="animate-pulse" />
                  <circle cx="700" cy="200" r="8" className="animate-pulse" />
                  <circle cx="800" cy="400" r="8" className="animate-pulse" />
                  <circle cx="700" cy="600" r="8" className="animate-pulse" />
                  <circle cx="300" cy="600" r="8" className="animate-pulse" />
                  <circle cx="200" cy="400" r="8" className="animate-pulse" />
                </g>
              </svg>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400 bg-clip-text text-transparent">
                    加入我们的社区
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  构建去中心化的信任网络，每个成员都是星座中闪耀的节点
                </p>
              </div>

              {/* 革命性星座网络布局 */}
              <div className="relative min-h-[700px] flex items-center justify-center">

                {/* 中心节点 - 社区核心 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-full p-12 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500">
                    <div className="absolute -inset-8 bg-gradient-to-r from-purple-500/30 via-blue-500/30 to-pink-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-20 h-20 bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span className="text-white font-bold text-2xl">🌟</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-4">AKASHA 社区</h3>
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-3xl font-bold text-purple-300">3000+</div>
                          <div className="text-white/70 text-sm">成员</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold text-blue-300">500+</div>
                          <div className="text-white/70 text-sm">NFT持有者</div>
                        </div>
                        <div>
                          <div className="text-3xl font-bold text-pink-300">50+</div>
                          <div className="text-white/70 text-sm">合作伙伴</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 上左节点 - Discord */}
                <div className="absolute top-[15%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-indigo-500/50 group-hover:border-indigo-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/30 to-purple-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">💬</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">Discord</h3>
                      <p className="text-indigo-300 text-sm">实时交流</p>
                      <p className="text-white/70 text-xs mt-2">24/7在线讨论</p>
                    </div>
                  </div>
                </div>

                {/* 上右节点 - Twitter */}
                <div className="absolute top-[15%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-sky-500/50 group-hover:border-sky-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-sky-500/30 to-blue-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🐦</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">Twitter</h3>
                      <p className="text-sky-300 text-sm">最新动态</p>
                      <p className="text-white/70 text-xs mt-2">项目更新发布</p>
                    </div>
                  </div>
                </div>

                {/* 右节点 - GitHub */}
                <div className="absolute top-1/2 right-[10%] transform -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-gray-500/50 group-hover:border-gray-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-gray-500/30 to-slate-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-gray-500 to-slate-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">⚡</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">GitHub</h3>
                      <p className="text-gray-300 text-sm">开源代码</p>
                      <p className="text-white/70 text-xs mt-2">技术透明</p>
                    </div>
                  </div>
                </div>

                {/* 下右节点 - 治理提案 */}
                <div className="absolute bottom-[15%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-emerald-500/50 group-hover:border-emerald-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-emerald-500/30 to-green-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🗳️</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">治理提案</h3>
                      <p className="text-emerald-300 text-sm">社区决策</p>
                      <p className="text-white/70 text-xs mt-2">民主投票</p>
                    </div>
                  </div>
                </div>

                {/* 下左节点 - 活动中心 */}
                <div className="absolute bottom-[15%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-orange-500/50 group-hover:border-orange-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-orange-500/30 to-red-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🎉</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">活动中心</h3>
                      <p className="text-orange-300 text-sm">社区活动</p>
                      <p className="text-white/70 text-xs mt-2">线上线下聚会</p>
                    </div>
                  </div>
                </div>

                {/* 左节点 - 学习资源 */}
                <div className="absolute top-1/2 left-[10%] transform -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/30 to-amber-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-amber-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">📚</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">学习资源</h3>
                      <p className="text-yellow-300 text-sm">知识分享</p>
                      <p className="text-white/70 text-xs mt-2">Web3教育</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* 页脚 */}
        <footer className="bg-slate-900 border-t border-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <p className="text-slate-400">
                © 2024 AKASHA DAO. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
