'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { useTranslation } from '@/components/providers/I18nProvider';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Logo } from '@/components/ui/Logo';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';

export default function Home() {
  const { address, isConnected } = useAccount();
  const { t, tArray } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // 动态背景效果
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.zIndex = '1';
    canvas.style.pointerEvents = 'none';
    document.body.appendChild(canvas);

    return () => {
      if (document.body.contains(canvas)) {
        document.body.removeChild(canvas);
      }
    };
  }, []);

  return (
    <>
      <div className="min-h-screen bg-black overflow-hidden relative">
        {/* 动态背景 */}
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-black"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
        </div>

        {/* 导航栏 */}
        <nav className="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-xl border-b border-purple-500/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-20">
              <Logo size="lg" showText={true} showSubtitle={true} href="/" />

              {/* 导航菜单 */}
              <div className="hidden md:flex items-center space-x-8">
                <a href="#about" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.about')}</a>
                <a href="#tiers" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">分层体系</a>
                <a href="#benefits" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">权益</a>
                <a href="#roadmap" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.roadmap')}</a>
                <a href="#community" className="text-slate-300 hover:text-purple-400 transition-all duration-300 font-medium">{t('nav.community')}</a>
              </div>

              <div className="flex items-center space-x-4">
                <LanguageSwitcher />
                <a
                  href="/mint"
                  className="px-8 py-3 bg-gradient-to-r from-purple-600 via-purple-500 to-blue-600 hover:from-purple-700 hover:via-purple-600 hover:to-blue-700 text-white rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/25 border border-purple-400/30"
                >
                  {t('nav.mint')}
                </a>
                <WalletButton />
              </div>
            </div>
          </div>
        </nav>

        {/* 主要内容 - 统一的能量流动设计系统 */}
        <main className="relative z-10">
          {/* 全局能量流动背景 */}
          <div className="fixed inset-0 pointer-events-none z-0">
            <svg width="100%" height="100%" className="absolute inset-0">
              <defs>
                <linearGradient id="energyFlow" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.1" />
                  <stop offset="25%" stopColor="#06B6D4" stopOpacity="0.1" />
                  <stop offset="50%" stopColor="#10B981" stopOpacity="0.1" />
                  <stop offset="75%" stopColor="#F59E0B" stopOpacity="0.1" />
                  <stop offset="100%" stopColor="#EF4444" stopOpacity="0.1" />
                </linearGradient>
                <radialGradient id="energyCore" cx="50%" cy="20%" r="30%">
                  <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
                  <stop offset="50%" stopColor="#06B6D4" stopOpacity="0.2" />
                  <stop offset="100%" stopColor="transparent" />
                </radialGradient>
              </defs>
              {/* 主能量流 - 连接所有部分的脊柱 */}
              <path
                d="M 50 100 Q 200 300 50 500 Q 200 700 50 900 Q 200 1100 50 1300 Q 200 1500 50 1700"
                stroke="url(#energyFlow)"
                strokeWidth="3"
                fill="none"
                strokeDasharray="20,10"
                className="animate-pulse"
              />
              <path
                d="M 950 100 Q 800 300 950 500 Q 800 700 950 900 Q 800 1100 950 1300 Q 800 1500 950 1700"
                stroke="url(#energyFlow)"
                strokeWidth="3"
                fill="none"
                strokeDasharray="20,10"
                className="animate-pulse"
                style={{animationDelay: '1s'}}
              />
              {/* 中央能量核心 */}
              <circle cx="50%" cy="20%" r="200" fill="url(#energyCore)" className="animate-pulse" />
            </svg>
          </div>

          {/* Hero 区域 - 能量核心 */}
          <section className="min-h-screen relative overflow-hidden pt-20 flex items-center justify-center">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

                {/* 左侧：主要内容 */}
                <div className="text-center lg:text-left">
                  <h1 className="text-6xl md:text-8xl font-black text-white mb-6">
                    <span className="bg-gradient-to-r from-purple-300 via-cyan-300 to-pink-300 bg-clip-text text-transparent animate-pulse">
                      AKASHA
                    </span>
                    <br />
                    <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent animate-pulse" style={{animationDelay: '0.5s'}}>
                      DAO
                    </span>
                  </h1>

                  <p className="text-xl md:text-2xl text-slate-300 mb-8 leading-relaxed">
                    神秘组织 · 信任象征 · 能量汇聚
                  </p>

                  <p className="text-lg text-slate-400 mb-12 max-w-2xl leading-relaxed">
                    AkashaDao是一个基于信任构建的去中心化社区，通过NFT身份认证系统，
                    为持有者提供独特的治理权益和专属社区体验。我们相信真正的价值来自于
                    社区成员之间的信任与协作。
                  </p>

                  {/* 核心特性 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                    <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                      <div className="text-3xl mb-3">🎭</div>
                      <h3 className="text-lg font-bold text-white mb-2">神秘身份</h3>
                      <p className="text-sm text-slate-400">匿名治理，身份保护，真正的去中心化社区</p>
                    </div>
                    <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-6 border border-cyan-500/20 hover:border-cyan-500/40 transition-all duration-300">
                      <div className="text-3xl mb-3">⚡</div>
                      <h3 className="text-lg font-bold text-white mb-2">能量治理</h3>
                      <p className="text-sm text-slate-400">基于持有量的投票权重，公平透明的决策机制</p>
                    </div>
                    <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-6 border border-green-500/20 hover:border-green-500/40 transition-all duration-300">
                      <div className="text-3xl mb-3">💎</div>
                      <h3 className="text-lg font-bold text-white mb-2">价值增长</h3>
                      <p className="text-sm text-slate-400">稀缺性设计，持有者专属权益，长期价值积累</p>
                    </div>
                  </div>

                  {/* 数据统计 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-purple-400 mb-1">10,000</div>
                      <div className="text-sm text-slate-400">总供应量</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-cyan-400 mb-1">3</div>
                      <div className="text-sm text-slate-400">能量层级</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-green-400 mb-1">∞</div>
                      <div className="text-sm text-slate-400">治理权限</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl md:text-3xl font-bold text-yellow-400 mb-1">24/7</div>
                      <div className="text-sm text-slate-400">社区活跃</div>
                    </div>
                  </div>

                  <a
                    href="/mint"
                    className="inline-block px-12 py-6 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 rounded-full font-bold text-white text-lg transition-all duration-500 hover:scale-110 shadow-2xl shadow-purple-500/50 hover:shadow-cyan-500/50"
                  >
                    启动能量铸造 ⚡
                  </a>
                </div>

                {/* 右侧：能量核心可视化 */}
                <div className="relative group flex justify-center">
                  {/* 核心能量场 */}
                  <div className="absolute -inset-16 bg-gradient-to-r from-purple-500/20 via-cyan-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse"></div>
                  <div className="absolute -inset-12 bg-gradient-to-r from-cyan-500/15 via-purple-500/15 to-blue-500/15 rounded-full blur-2xl animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  <div className="absolute -inset-8 bg-gradient-to-r from-pink-500/10 via-yellow-500/10 to-purple-500/10 rounded-full blur-xl animate-pulse" style={{animationDelay: '1s'}}></div>

                  {/* 主核心 */}
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-full p-16 border-2 border-white/20 group-hover:border-white/40 transition-all duration-1000">
                    <div className="text-center">
                      <div className="text-6xl mb-4">⚡</div>
                      <div className="text-white font-bold text-xl mb-2">能量核心</div>
                      <div className="text-slate-400 text-sm">驱动整个生态系统</div>
                    </div>
                  </div>

                  {/* 环绕能量轨道 */}
                  <div className="absolute inset-0 animate-spin" style={{animationDuration: '60s'}}>
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-purple-500 rounded-full blur-sm opacity-60"></div>
                    <div className="absolute top-1/2 -right-4 transform -translate-y-1/2 w-6 h-6 bg-cyan-500 rounded-full blur-sm opacity-60"></div>
                    <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-pink-500 rounded-full blur-sm opacity-60"></div>
                    <div className="absolute top-1/2 -left-4 transform -translate-y-1/2 w-6 h-6 bg-yellow-500 rounded-full blur-sm opacity-60"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* 能量射线指向下一部分 */}
            <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2">
              <div className="w-1 h-20 bg-gradient-to-b from-purple-500/50 to-transparent animate-pulse"></div>
              <div className="w-3 h-3 bg-purple-500 rounded-full mx-auto animate-bounce"></div>
            </div>
          </section>

          {/* 分层体系区域 - 能量层级分布 */}
          <section id="tiers" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            {/* 能量接收器 - 从上方Hero接收能量 */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-6 h-6 bg-purple-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-16 bg-gradient-to-b from-purple-500 to-transparent mx-auto"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-cyan-400 to-blue-400 bg-clip-text text-transparent">
                    能量层级
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  能量在不同层级间流动，构建有机的社区生态系统
                </p>
              </div>

              {/* 能量层级分布 - 垂直流动设计 */}
              <div className="relative flex flex-col items-center space-y-16">

                {/* 顶层能量节点 - 白名单层 */}
                <div className="relative group">
                  {/* 能量场 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-cyan-500/20 rounded-full blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 min-w-[500px]">
                    <div className="text-center mb-6">
                      <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <span className="text-white font-bold text-2xl">👑</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-3">白名单层</h3>
                      <p className="text-purple-300 mb-4">精英投资人 · 最高能量级</p>
                      <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg p-4 mb-6">
                        <div className="text-3xl font-bold text-white mb-1">0.08 ETH</div>
                        <div className="text-purple-300 text-sm">铸造价格</div>
                      </div>
                    </div>

                    {/* 详细权益 */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                      <div className="bg-purple-500/10 rounded-lg p-3 text-center">
                        <div className="text-purple-400 font-semibold text-sm mb-1">治理权重</div>
                        <div className="text-white font-bold">5x 投票权</div>
                      </div>
                      <div className="bg-purple-500/10 rounded-lg p-3 text-center">
                        <div className="text-purple-400 font-semibold text-sm mb-1">专属权益</div>
                        <div className="text-white font-bold">VIP 通道</div>
                      </div>
                      <div className="bg-purple-500/10 rounded-lg p-3 text-center">
                        <div className="text-purple-400 font-semibold text-sm mb-1">收益分成</div>
                        <div className="text-white font-bold">50% 分红</div>
                      </div>
                    </div>

                    <div className="text-xs text-slate-400 text-center leading-relaxed">
                      优先参与所有项目决策 • 独家投资机会获取<br />
                      专属社区频道访问 • 年度线下聚会邀请
                    </div>
                  </div>

                  {/* 能量传输线 */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-16 bg-gradient-to-b from-purple-500/50 to-cyan-500/50 animate-pulse"></div>
                  </div>
                </div>

                {/* 中层能量节点 - 学生层 */}
                <div className="relative group">
                  {/* 能量场 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-green-500/20 rounded-full blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-cyan-500/50 group-hover:border-cyan-500/80 transition-all duration-500 min-w-[500px]">
                    <div className="text-center mb-6">
                      <div className="w-20 h-20 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <span className="text-white font-bold text-2xl">🎓</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-3">学生层</h3>
                      <p className="text-cyan-300 mb-4">创业者社区 · 中等能量级</p>
                      <div className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-lg p-4 mb-6">
                        <div className="text-3xl font-bold text-white mb-1">0.05 ETH</div>
                        <div className="text-cyan-300 text-sm">铸造价格</div>
                      </div>
                    </div>

                    {/* 详细权益 */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                      <div className="bg-cyan-500/10 rounded-lg p-3 text-center">
                        <div className="text-cyan-400 font-semibold text-sm mb-1">治理权重</div>
                        <div className="text-white font-bold">3x 投票权</div>
                      </div>
                      <div className="bg-cyan-500/10 rounded-lg p-3 text-center">
                        <div className="text-cyan-400 font-semibold text-sm mb-1">学习资源</div>
                        <div className="text-white font-bold">免费课程</div>
                      </div>
                      <div className="bg-cyan-500/10 rounded-lg p-3 text-center">
                        <div className="text-cyan-400 font-semibold text-sm mb-1">收益分成</div>
                        <div className="text-white font-bold">30% 分红</div>
                      </div>
                    </div>

                    <div className="text-xs text-slate-400 text-center leading-relaxed">
                      参与大部分社区决策 • 创业项目孵化支持<br />
                      导师匹配服务 • 月度线上分享会
                    </div>
                  </div>

                  {/* 能量传输线 */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-16 bg-gradient-to-b from-cyan-500/50 to-green-500/50 animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  </div>
                </div>

                {/* 底层能量节点 - 公开层 */}
                <div className="relative group">
                  {/* 能量场 */}
                  <div className="absolute -inset-8 bg-gradient-to-r from-green-500/20 via-yellow-500/20 to-orange-500/20 rounded-full blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500 min-w-[500px]">
                    <div className="text-center mb-6">
                      <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                        <span className="text-white font-bold text-2xl">🌍</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-3">公开层</h3>
                      <p className="text-green-300 mb-4">社区成员 · 基础能量级</p>
                      <div className="bg-gradient-to-r from-green-500/20 to-yellow-500/20 rounded-lg p-4 mb-6">
                        <div className="text-3xl font-bold text-white mb-1">0.03 ETH</div>
                        <div className="text-green-300 text-sm">铸造价格</div>
                      </div>
                    </div>

                    {/* 详细权益 */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                      <div className="bg-green-500/10 rounded-lg p-3 text-center">
                        <div className="text-green-400 font-semibold text-sm mb-1">治理权重</div>
                        <div className="text-white font-bold">1x 投票权</div>
                      </div>
                      <div className="bg-green-500/10 rounded-lg p-3 text-center">
                        <div className="text-green-400 font-semibold text-sm mb-1">社区访问</div>
                        <div className="text-white font-bold">基础频道</div>
                      </div>
                      <div className="bg-green-500/10 rounded-lg p-3 text-center">
                        <div className="text-green-400 font-semibold text-sm mb-1">收益分成</div>
                        <div className="text-white font-bold">20% 分红</div>
                      </div>
                    </div>

                    <div className="text-xs text-slate-400 text-center leading-relaxed">
                      参与基础社区治理 • 获取项目最新资讯<br />
                      社区活动参与权 • 基础学习资源访问
                    </div>
                  </div>

                  {/* 能量传输到下一部分 */}
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-16 bg-gradient-to-b from-green-500/50 to-transparent animate-pulse" style={{animationDelay: '1s'}}></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full mx-auto animate-bounce"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 核心价值区域 - 能量共振网络 */}
          <section id="about" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            {/* 能量接收器 */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-6 h-6 bg-green-500 rounded-full animate-pulse"></div>
              <div className="w-1 h-16 bg-gradient-to-b from-green-500 to-transparent mx-auto"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                    能量共振
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  四大核心价值形成能量共振网络，驱动整个生态系统的持续发展
                </p>
              </div>

              {/* 能量共振网络 - 四象限布局 */}
              <div className="relative min-h-[600px] flex items-center justify-center">

                {/* 中央能量核心 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
                  <div className="relative">
                    <div className="absolute -inset-6 bg-gradient-to-r from-purple-500/30 via-cyan-500/30 to-green-500/30 rounded-full blur-2xl animate-pulse"></div>
                    <div className="relative w-16 h-16 bg-gradient-to-r from-purple-500 via-cyan-500 to-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xl">⚡</span>
                    </div>
                  </div>
                </div>

                {/* 上方能量节点 - 神秘IP */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 min-w-[350px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">🎭</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">神秘IP</h3>
                      <p className="text-purple-300 text-sm mb-4">维持神秘人设 · 信任为锚点</p>

                      {/* 详细说明 */}
                      <div className="bg-purple-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-purple-200 leading-relaxed">
                          通过匿名化身份系统，保护成员隐私的同时建立基于行为的信任机制
                        </div>
                      </div>

                      <div className="flex justify-center space-x-2">
                        <span className="px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300">匿名治理</span>
                        <span className="px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300">身份保护</span>
                      </div>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-12 bg-gradient-to-b from-purple-500/50 to-transparent animate-pulse"></div>
                  </div>
                </div>

                {/* 右方能量节点 - 价值共创 */}
                <div className="absolute top-1/2 right-0 transform -translate-y-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-cyan-500/50 group-hover:border-cyan-500/80 transition-all duration-500 min-w-[350px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">💎</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">价值共创</h3>
                      <p className="text-cyan-300 text-sm mb-4">社区共建 · 价值共享</p>

                      {/* 详细说明 */}
                      <div className="bg-cyan-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-cyan-200 leading-relaxed">
                          每个成员都是价值创造者，通过贡献获得相应回报，实现真正的共建共享
                        </div>
                      </div>

                      <div className="flex justify-center space-x-2">
                        <span className="px-2 py-1 bg-cyan-500/20 rounded text-xs text-cyan-300">贡献激励</span>
                        <span className="px-2 py-1 bg-cyan-500/20 rounded text-xs text-cyan-300">价值分配</span>
                      </div>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute top-1/2 right-full transform -translate-y-1/2">
                    <div className="w-12 h-1 bg-gradient-to-l from-cyan-500/50 to-transparent animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  </div>
                </div>

                {/* 下方能量节点 - 生态发展 */}
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500 min-w-[350px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-green-500/20 to-yellow-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">🌟</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">生态发展</h3>
                      <p className="text-green-300 text-sm mb-4">持续创新 · 生态繁荣</p>

                      {/* 详细说明 */}
                      <div className="bg-green-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-green-200 leading-relaxed">
                          构建可持续发展的生态系统，通过创新驱动长期价值增长
                        </div>
                      </div>

                      <div className="flex justify-center space-x-2">
                        <span className="px-2 py-1 bg-green-500/20 rounded text-xs text-green-300">技术创新</span>
                        <span className="px-2 py-1 bg-green-500/20 rounded text-xs text-green-300">生态扩展</span>
                      </div>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2">
                    <div className="w-1 h-12 bg-gradient-to-t from-green-500/50 to-transparent animate-pulse" style={{animationDelay: '1s'}}></div>
                  </div>
                </div>

                {/* 左方能量节点 - 信任为本 */}
                <div className="absolute top-1/2 left-0 transform -translate-y-1/2 group">
                  <div className="relative bg-black/70 backdrop-blur-2xl rounded-2xl p-8 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500 min-w-[350px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-2xl blur-xl opacity-60 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white text-2xl">🤝</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-3">信任为本</h3>
                      <p className="text-yellow-300 text-sm mb-4">持续价值输出 · 维护社区信任</p>

                      {/* 详细说明 */}
                      <div className="bg-yellow-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-yellow-200 leading-relaxed">
                          建立透明的信任机制，通过持续的价值输出赢得社区成员的长期信任
                        </div>
                      </div>

                      <div className="flex justify-center space-x-2">
                        <span className="px-2 py-1 bg-yellow-500/20 rounded text-xs text-yellow-300">透明治理</span>
                        <span className="px-2 py-1 bg-yellow-500/20 rounded text-xs text-yellow-300">信誉系统</span>
                      </div>
                    </div>
                  </div>
                  {/* 能量连接线 */}
                  <div className="absolute top-1/2 left-full transform -translate-y-1/2">
                    <div className="w-12 h-1 bg-gradient-to-r from-yellow-500/50 to-transparent animate-pulse" style={{animationDelay: '1.5s'}}></div>
                  </div>
                </div>

                {/* 能量传输到下一部分 */}
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <div className="w-1 h-16 bg-gradient-to-b from-green-500/50 to-transparent animate-pulse" style={{animationDelay: '2s'}}></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full mx-auto animate-bounce"></div>
                </div>
              </div>
            </div>
          </section>

          {/* 路线图部分 - 革命性螺旋时间线布局 */}
          <section id="roadmap" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-slate-900/50">
            {/* 螺旋时间线背景 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <svg width="800" height="800" viewBox="0 0 800 800" className="opacity-20">
                <defs>
                  <linearGradient id="timelineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="25%" stopColor="#06B6D4" />
                    <stop offset="50%" stopColor="#10B981" />
                    <stop offset="75%" stopColor="#F59E0B" />
                    <stop offset="100%" stopColor="#EF4444" />
                  </linearGradient>
                </defs>
                {/* 螺旋时间线路径 */}
                <path
                  d="M400,400 Q500,300 600,400 Q700,500 600,600 Q500,700 400,600 Q300,500 400,400 Q450,350 500,400 Q550,450 500,500 Q450,550 400,500 Q350,450 400,400"
                  fill="none"
                  stroke="url(#timelineGradient)"
                  strokeWidth="3"
                  strokeDasharray="20,10"
                  className="animate-pulse"
                />
                {/* 时间节点 */}
                <circle cx="400" cy="400" r="8" fill="#8B5CF6" className="animate-pulse" />
                <circle cx="500" cy="300" r="8" fill="#06B6D4" className="animate-pulse" />
                <circle cx="600" cy="500" r="8" fill="#10B981" className="animate-pulse" />
                <circle cx="500" cy="700" r="8" fill="#F59E0B" className="animate-pulse" />
                <circle cx="300" cy="500" r="8" fill="#EF4444" className="animate-pulse" />
              </svg>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-green-400 bg-clip-text text-transparent">
                    发展路线图
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  以螺旋式发展路径，构建去中心化社区生态系统
                </p>
              </div>

              {/* 革命性螺旋时间线布局 */}
              <div className="relative min-h-[800px] flex items-center justify-center">

                {/* 中心起点 - 项目启动 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-8 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 min-w-[280px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">🚀</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">项目启动</h3>
                      <p className="text-purple-300 text-sm mb-3">2024 Q1</p>

                      <div className="bg-purple-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-purple-200 leading-relaxed">
                          • 智能合约开发与审计<br />
                          • 官网与社区平台搭建<br />
                          • 核心团队组建<br />
                          • 白皮书发布
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-purple-500/20 rounded text-xs text-purple-300">已完成</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 第一圈 - NFT发布 */}
                <div className="absolute top-[25%] right-[20%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-blue-500/50 group-hover:border-blue-500/80 transition-all duration-500 min-w-[280px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">🎨</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">NFT发布</h3>
                      <p className="text-blue-300 text-sm mb-3">2024 Q2</p>

                      <div className="bg-blue-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-blue-200 leading-relaxed">
                          • 10,000个独特NFT设计<br />
                          • 三层分级铸造系统<br />
                          • 稀有度算法实现<br />
                          • 元数据存储优化
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300">进行中</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 第二圈 - 社区治理 */}
                <div className="absolute bottom-[20%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-green-500/50 group-hover:border-green-500/80 transition-all duration-500 min-w-[280px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-green-500/30 to-emerald-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">🗳️</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">社区治理</h3>
                      <p className="text-green-300 text-sm mb-3">2024 Q3</p>

                      <div className="bg-green-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-green-200 leading-relaxed">
                          • DAO治理框架建立<br />
                          • 提案投票系统上线<br />
                          • 社区基金管理<br />
                          • 权重分配机制
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-green-500/20 rounded text-xs text-green-300">规划中</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 第三圈 - 生态扩展 */}
                <div className="absolute bottom-[25%] left-[20%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500 min-w-[280px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">🌐</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">生态扩展</h3>
                      <p className="text-yellow-300 text-sm mb-3">2024 Q4</p>

                      <div className="bg-yellow-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-yellow-200 leading-relaxed">
                          • 战略合作伙伴引入<br />
                          • DeFi协议集成<br />
                          • 跨链桥接开发<br />
                          • 生态激励计划
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-yellow-500/20 rounded text-xs text-yellow-300">规划中</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 第四圈 - 全球化 */}
                <div className="absolute top-[20%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-red-500/50 group-hover:border-red-500/80 transition-all duration-500 min-w-[280px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-red-500/30 to-pink-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-lg">🌍</span>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">全球化</h3>
                      <p className="text-red-300 text-sm mb-3">2025 Q1</p>

                      <div className="bg-red-500/10 rounded-lg p-3 mb-3">
                        <div className="text-xs text-red-200 leading-relaxed">
                          • 多语言社区建设<br />
                          • 全球市场推广<br />
                          • 多链生态部署<br />
                          • 国际合规认证
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-red-500/20 rounded text-xs text-red-300">未来规划</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 连接线动画 */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 800 600">
                  <path
                    d="M400,300 Q500,250 550,350 Q600,450 500,500 Q400,550 350,450 Q300,350 400,300"
                    fill="none"
                    stroke="url(#timelineGradient)"
                    strokeWidth="2"
                    strokeDasharray="8,4"
                    className="animate-pulse"
                  />
                </svg>
              </div>
            </div>
          </section>

          {/* 社区部分 - 革命性星座网络布局 */}
          <section id="community" className="py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-slate-800/30">
            {/* 星座背景装饰 */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <svg width="1000" height="800" viewBox="0 0 1000 800" className="opacity-20">
                <defs>
                  <linearGradient id="constellationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="20%" stopColor="#06B6D4" />
                    <stop offset="40%" stopColor="#10B981" />
                    <stop offset="60%" stopColor="#F59E0B" />
                    <stop offset="80%" stopColor="#EF4444" />
                    <stop offset="100%" stopColor="#EC4899" />
                  </linearGradient>
                </defs>
                {/* 星座连接线 */}
                <g stroke="url(#constellationGradient)" strokeWidth="2" strokeDasharray="5,5" className="animate-pulse">
                  <line x1="500" y1="400" x2="300" y2="200" />
                  <line x1="500" y1="400" x2="700" y2="200" />
                  <line x1="500" y1="400" x2="800" y2="400" />
                  <line x1="500" y1="400" x2="700" y2="600" />
                  <line x1="500" y1="400" x2="300" y2="600" />
                  <line x1="500" y1="400" x2="200" y2="400" />
                </g>
                {/* 星座节点 */}
                <g fill="url(#constellationGradient)">
                  <circle cx="500" cy="400" r="12" className="animate-pulse" />
                  <circle cx="300" cy="200" r="8" className="animate-pulse" />
                  <circle cx="700" cy="200" r="8" className="animate-pulse" />
                  <circle cx="800" cy="400" r="8" className="animate-pulse" />
                  <circle cx="700" cy="600" r="8" className="animate-pulse" />
                  <circle cx="300" cy="600" r="8" className="animate-pulse" />
                  <circle cx="200" cy="400" r="8" className="animate-pulse" />
                </g>
              </svg>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              {/* 标题 */}
              <div className="text-center mb-20">
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400 bg-clip-text text-transparent">
                    加入我们的社区
                  </span>
                </h2>
                <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
                  构建去中心化的信任网络，每个成员都是星座中闪耀的节点
                </p>
              </div>

              {/* 革命性星座网络布局 */}
              <div className="relative min-h-[700px] flex items-center justify-center">

                {/* 中心节点 - 社区核心 */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-12 border-2 border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 min-w-[400px]">
                    <div className="absolute -inset-8 bg-gradient-to-r from-purple-500/30 via-blue-500/30 to-pink-500/30 blur-3xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-20 h-20 bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span className="text-white font-bold text-2xl">🌟</span>
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-4">AKASHA 社区</h3>

                      {/* 社区统计 */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
                        <div className="bg-purple-500/10 rounded-lg p-3">
                          <div className="text-2xl font-bold text-purple-300">5,000+</div>
                          <div className="text-white/70 text-xs">社区成员</div>
                        </div>
                        <div className="bg-blue-500/10 rounded-lg p-3">
                          <div className="text-2xl font-bold text-blue-300">1,200+</div>
                          <div className="text-white/70 text-xs">NFT持有者</div>
                        </div>
                        <div className="bg-green-500/10 rounded-lg p-3">
                          <div className="text-2xl font-bold text-green-300">80+</div>
                          <div className="text-white/70 text-xs">合作伙伴</div>
                        </div>
                        <div className="bg-pink-500/10 rounded-lg p-3">
                          <div className="text-2xl font-bold text-pink-300">24/7</div>
                          <div className="text-white/70 text-xs">在线支持</div>
                        </div>
                      </div>

                      {/* 社区特色 */}
                      <div className="text-xs text-slate-300 leading-relaxed">
                        全球化去中心化社区 • 多元化治理机制<br />
                        创新项目孵化 • 价值共创生态
                      </div>
                    </div>
                  </div>
                </div>

                {/* 上左节点 - Discord */}
                <div className="absolute top-[15%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-indigo-500/50 group-hover:border-indigo-500/80 transition-all duration-500 min-w-[220px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/30 to-purple-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">💬</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">Discord</h3>
                      <p className="text-indigo-300 text-sm mb-3">实时交流中心</p>

                      <div className="bg-indigo-500/10 rounded-lg p-2 mb-3">
                        <div className="text-xs text-indigo-200 leading-relaxed">
                          • 专属频道讨论<br />
                          • 实时项目更新<br />
                          • 社区活动通知<br />
                          • 技术支持服务
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-indigo-500/20 rounded text-xs text-indigo-300">3000+ 活跃用户</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 上右节点 - Twitter */}
                <div className="absolute top-[15%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-sky-500/50 group-hover:border-sky-500/80 transition-all duration-500 min-w-[220px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-sky-500/30 to-blue-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-sky-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">🐦</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">Twitter</h3>
                      <p className="text-sky-300 text-sm mb-3">官方动态发布</p>

                      <div className="bg-sky-500/10 rounded-lg p-2 mb-3">
                        <div className="text-xs text-sky-200 leading-relaxed">
                          • 项目重要公告<br />
                          • 市场动态分析<br />
                          • 社区互动活动<br />
                          • 合作伙伴资讯
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-sky-500/20 rounded text-xs text-sky-300">10K+ 关注者</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 右节点 - GitHub */}
                <div className="absolute top-1/2 right-[10%] transform -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-gray-500/50 group-hover:border-gray-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-gray-500/30 to-slate-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-gray-500 to-slate-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">⚡</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">GitHub</h3>
                      <p className="text-gray-300 text-sm">开源代码</p>
                      <p className="text-white/70 text-xs mt-2">技术透明</p>
                    </div>
                  </div>
                </div>

                {/* 下右节点 - 治理提案 */}
                <div className="absolute bottom-[15%] right-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-emerald-500/50 group-hover:border-emerald-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-emerald-500/30 to-green-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🗳️</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">治理提案</h3>
                      <p className="text-emerald-300 text-sm">社区决策</p>
                      <p className="text-white/70 text-xs mt-2">民主投票</p>
                    </div>
                  </div>
                </div>

                {/* 下左节点 - 活动中心 */}
                <div className="absolute bottom-[15%] left-[25%] group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-orange-500/50 group-hover:border-orange-500/80 transition-all duration-500">
                    <div className="absolute -inset-4 bg-gradient-to-r from-orange-500/30 to-red-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold">🎉</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">活动中心</h3>
                      <p className="text-orange-300 text-sm">社区活动</p>
                      <p className="text-white/70 text-xs mt-2">线上线下聚会</p>
                    </div>
                  </div>
                </div>

                {/* 左节点 - 学习资源 */}
                <div className="absolute top-1/2 left-[10%] transform -translate-y-1/2 group">
                  <div className="relative bg-black/60 backdrop-blur-2xl rounded-2xl p-6 border-2 border-yellow-500/50 group-hover:border-yellow-500/80 transition-all duration-500 min-w-[220px]">
                    <div className="absolute -inset-4 bg-gradient-to-r from-yellow-500/30 to-amber-500/30 blur-2xl opacity-50 group-hover:opacity-80 transition-all duration-1000"></div>
                    <div className="relative text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-amber-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-white font-bold text-xl">📚</span>
                      </div>
                      <h3 className="text-lg font-bold text-white mb-2">学习资源</h3>
                      <p className="text-yellow-300 text-sm mb-3">Web3知识中心</p>

                      <div className="bg-yellow-500/10 rounded-lg p-2 mb-3">
                        <div className="text-xs text-yellow-200 leading-relaxed">
                          • 区块链基础教程<br />
                          • DeFi操作指南<br />
                          • NFT投资策略<br />
                          • 智能合约开发
                        </div>
                      </div>

                      <div className="flex justify-center">
                        <span className="px-2 py-1 bg-yellow-500/20 rounded text-xs text-yellow-300">100+ 教程</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* 页脚 */}
        <footer className="bg-slate-900 border-t border-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <p className="text-slate-400">
                © 2024 AKASHA DAO. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
