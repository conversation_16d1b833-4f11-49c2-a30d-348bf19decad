'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintSection } from '@/components/nft/MintSection';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { useWhitelist } from '@/hooks/useWhitelist';
import { Logo } from '@/components/ui/Logo';
import { useTranslation } from '@/components/providers/I18nProvider';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const { whitelistData, studentData, isLoading } = useWhitelist();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');
  const { t, tArray } = useTranslation();

  return (
    <div className="min-h-screen bg-black overflow-hidden relative">
      {/* 现代Web3背景系统 */}
      <div className="fixed inset-0 z-0">
        {/* 主背景 */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-60"
          style={{
            backgroundImage: 'url(/images/web3-hero-background.webp)',
          }}
        />

        {/* 深色叠加层 */}
        <div className="absolute inset-0 bg-black/70"></div>

        {/* Web3渐变装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/30 via-transparent to-blue-900/30"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-purple-500/5 to-cyan-500/10"></div>

        {/* 动态几何光效 */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* 简洁Header */}
      <header className="relative z-20 flex justify-between items-center p-6 bg-black/30 backdrop-blur-xl">
        <Logo size="md" showText={true} href="/" />
        <WalletButton />
      </header>

      {/* 主要内容区域 */}
      <div className="relative z-10 min-h-screen flex items-center">
        <div className="container mx-auto px-6">
          <div className="max-w-7xl mx-auto">

            {/* 顶部标题区域 - 现代Web3风格 */}
            <div className="text-center mb-16">
              <div className="relative">
                {/* 发光效果背景 */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-blue-600/20 to-cyan-600/20 blur-3xl"></div>

                <h1 className="relative text-5xl md:text-7xl font-bold text-white mb-6">
                  <span className="bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
                    AKASHA DAO
                  </span>
                </h1>
              </div>

              <p className="text-2xl text-white/80 mb-4 font-medium">
                {t('mint.subtitle')}
              </p>
              <p className="text-lg text-white/60 mb-12 max-w-2xl mx-auto">
                {t('mint.description')}
              </p>

              {/* 现代化层级选择器 */}
              <div className="flex justify-center space-x-6 mb-12">
                <button
                  onClick={() => setSelectedTier('whitelist')}
                  className={`group relative px-8 py-4 rounded-2xl font-semibold transition-all duration-300 ${
                    selectedTier === 'whitelist'
                      ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg shadow-purple-500/25'
                      : 'bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm border border-white/20'
                  }`}
                >
                  <span className="relative z-10">{t('mint.whitelist.title')}</span>
                  {selectedTier === 'whitelist' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl blur opacity-50 group-hover:opacity-75 transition-opacity"></div>
                  )}
                </button>
                <button
                  onClick={() => setSelectedTier('student')}
                  className={`group relative px-8 py-4 rounded-2xl font-semibold transition-all duration-300 ${
                    selectedTier === 'student'
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg shadow-blue-500/25'
                      : 'bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm border border-white/20'
                  }`}
                >
                  <span className="relative z-10">{t('mint.student.title')}</span>
                  {selectedTier === 'student' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl blur opacity-50 group-hover:opacity-75 transition-opacity"></div>
                  )}
                </button>
              </div>
            </div>

            {/* 主要布局 - 居中卡片式设计 */}
            <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {/* 左侧 - NFT展示 */}
              <div className="space-y-8">
                {/* NFT预览卡片 - 现代Web3设计 */}
                <div className="relative group">
                  {/* 多层发光效果 */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-purple-500/30 via-blue-500/30 to-cyan-500/30 rounded-3xl blur-xl opacity-40 group-hover:opacity-60 transition duration-1000"></div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-400/50 via-blue-400/50 to-cyan-400/50 rounded-2xl blur opacity-50 group-hover:opacity-70 transition duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-xl rounded-2xl p-8 border border-white/30 group-hover:border-white/50 transition-all duration-300">
                    <div className="text-center mb-8">
                      <div className="relative">
                        <h3 className="text-2xl font-bold text-white mb-3">
                          <span className={`bg-gradient-to-r ${selectedTier === 'whitelist' ? 'from-purple-400 to-blue-400' : 'from-blue-400 to-cyan-400'} bg-clip-text text-transparent`}>
                            {selectedTier === 'whitelist' ? t('mint.whitelist.title') : t('mint.student.title')}
                          </span>
                        </h3>
                        <p className="text-white/70 text-lg">
                          {selectedTier === 'whitelist' ? t('mint.whitelist.description') : t('mint.student.description')}
                        </p>
                      </div>
                    </div>

                    {/* NFT预览 - 增强视觉效果 */}
                    <div className="relative mb-8">
                      <div className="relative group/nft">
                        {/* NFT发光背景 */}
                        <div className={`absolute -inset-4 bg-gradient-to-r ${selectedTier === 'whitelist' ? 'from-purple-500/20 to-blue-500/20' : 'from-blue-500/20 to-cyan-500/20'} rounded-3xl blur-2xl opacity-60 group-hover/nft:opacity-80 transition-opacity`}></div>

                        <NFTPreview tier={selectedTier} className="relative w-full max-w-sm mx-auto transform group-hover/nft:scale-105 transition-transform duration-300" />

                        {/* 装饰几何元素 */}
                        <div
                          className="absolute -inset-6 opacity-10 rounded-3xl"
                          style={{
                            backgroundImage: 'url(/images/web3-decorative-elements.webp)',
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* NFT特性 - 现代化列表 */}
                    <div className="space-y-6">
                      <h4 className="text-xl font-bold text-white text-center">{t('mint.benefits.title')}</h4>
                      <div className="space-y-3">
                        {(selectedTier === 'whitelist' ? tArray('mint.whitelist.benefits') : tArray('mint.student.benefits')).map((benefit: string, index: number) => (
                          <div key={index} className="flex items-center text-white/90 p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-colors">
                            <div className={`w-6 h-6 rounded-full ${selectedTier === 'whitelist' ? 'bg-gradient-to-r from-purple-400 to-blue-400' : 'bg-gradient-to-r from-blue-400 to-cyan-400'} flex items-center justify-center mr-4 flex-shrink-0`}>
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <span className="font-medium">{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 侧边装饰 */}
                <div 
                  className="absolute -left-20 top-1/2 transform -translate-y-1/2 w-40 h-80 opacity-10 pointer-events-none"
                  style={{
                    backgroundImage: 'url(/images/mint-sidebar-decoration.webp)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                ></div>
              </div>

              {/* 右侧 - 铸造界面 */}
              <div className="space-y-8">
                {/* 主要铸造卡片 - 现代Web3设计 */}
                <div className="relative group">
                  {/* 多层发光效果 */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-cyan-500/30 via-purple-500/30 to-pink-500/30 rounded-3xl blur-xl opacity-40 group-hover:opacity-60 transition duration-1000"></div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-cyan-400/50 via-purple-400/50 to-pink-400/50 rounded-2xl blur opacity-50 group-hover:opacity-70 transition duration-1000"></div>

                  <div className="relative bg-black/70 backdrop-blur-xl rounded-2xl p-8 border border-white/30 group-hover:border-white/50 transition-all duration-300">
                    <div className="text-center mb-8">
                      <div className="relative">
                        <h2 className="text-3xl font-bold text-white mb-3">
                          <span className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                            {t('mint.title')}
                          </span>
                        </h2>
                        <p className="text-white/70 text-lg">{t('mint.description')}</p>
                      </div>
                    </div>

                    {/* Mint Section */}
                    <MintSection
                      tier={selectedTier}
                      whitelistData={whitelistData}
                      studentData={studentData}
                      isLoading={isLoading}
                    />
                  </div>
                </div>

                {/* 技术信息卡片 - 现代化设计 */}
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>

                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-6 border border-white/20 group-hover:border-white/30 transition-all duration-300">
                    <h3 className="text-xl font-bold text-white mb-6 text-center">
                      <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                        {t('mint.info.title')}
                      </span>
                    </h3>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                        <span className="text-white/80 font-medium">{t('mint.info.network')}</span>
                        <span className="text-green-400 font-bold flex items-center">
                          <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                          Ethereum
                        </span>
                      </div>
                      <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                        <span className="text-white/80 font-medium">{t('mint.info.standard')}</span>
                        <span className="text-blue-400 font-bold">ERC-721</span>
                      </div>
                      <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                        <span className="text-white/80 font-medium">{t('mint.info.supply')}</span>
                        <span className="text-purple-400 font-bold">{t('mint.info.limited')}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 神秘元素装饰 */}
                <div className="relative">
                  <div
                    className="absolute -right-20 top-1/2 transform -translate-y-1/2 w-40 h-80 opacity-5 pointer-events-none"
                    style={{
                      backgroundImage: 'url(/images/akasha-mysterious-figure.webp)',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                    }}
                  ></div>
                </div>
              </div>
            </div>

            {/* 底部说明 - 现代化设计 */}
            <div className="text-center mt-20">
              <div className="relative max-w-4xl mx-auto">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-blue-500/10 to-cyan-500/10 rounded-2xl blur-xl"></div>
                <div className="relative bg-black/40 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                  <div className="flex items-center justify-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full flex items-center justify-center mr-3">
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h4 className="text-lg font-semibold text-white">重要说明</h4>
                  </div>
                  <p className="text-white/70 text-base leading-relaxed">
                    {t('mint.disclaimer')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
