'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintSection } from '@/components/nft/MintSection';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { useWhitelist } from '@/hooks/useWhitelist';
import { Logo } from '@/components/ui/Logo';
import { useTranslation } from '@/hooks/useTranslation';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const { whitelistData, studentData, isLoading } = useWhitelist();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');
  const { t, tArray } = useTranslation();

  return (
    <div className="min-h-screen relative">
      {/* 奢华背景系统 */}
      <div className="fixed inset-0 z-0">
        {/* 主背景 */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/luxury-mint-background.webp)',
          }}
        ></div>

        {/* 深色叠加层 */}
        <div className="absolute inset-0 bg-black/60"></div>

        {/* 渐变装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-amber-900/20"></div>

        {/* 动态光效 */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-purple-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-amber-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* 简洁Header */}
      <header className="relative z-20 flex justify-between items-center p-6 bg-black/30 backdrop-blur-xl">
        <Logo size="md" showText={true} href="/" />
        <WalletButton />
      </header>

      {/* 主要内容区域 */}
      <div className="relative z-10 min-h-screen flex items-center">
        <div className="container mx-auto px-6">
          <div className="max-w-7xl mx-auto">

            {/* 顶部标题区域 */}
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
                <span className="bg-gradient-to-r from-amber-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  AKASHA DAO
                </span>
              </h1>
              <p className="text-xl text-white/70 mb-8">{t('mint.subtitle')}</p>
              
              {/* 层级选择器 */}
              <div className="flex justify-center space-x-4 mb-8">
                <button
                  onClick={() => setSelectedTier('whitelist')}
                  className={`px-6 py-3 rounded-xl font-medium transition-all ${
                    selectedTier === 'whitelist'
                      ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-black'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {t('mint.whitelist.title')}
                </button>
                <button
                  onClick={() => setSelectedTier('student')}
                  className={`px-6 py-3 rounded-xl font-medium transition-all ${
                    selectedTier === 'student'
                      ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  {t('mint.student.title')}
                </button>
              </div>
            </div>

            {/* 主要布局 - 居中卡片式设计 */}
            <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {/* 左侧 - NFT展示 */}
              <div className="space-y-8">
                {/* NFT预览卡片 */}
                <div className="relative group">
                  {/* 发光效果 */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-amber-500/50 via-purple-500/50 to-cyan-500/50 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>
                  
                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-white mb-2">
                        {selectedTier === 'whitelist' ? t('mint.whitelist.title') : t('mint.student.title')}
                      </h3>
                      <p className="text-white/60">
                        {selectedTier === 'whitelist' ? t('mint.whitelist.description') : t('mint.student.description')}
                      </p>
                    </div>

                    {/* NFT预览 */}
                    <div className="relative">
                      <NFTPreview tier={selectedTier} className="w-full max-w-sm mx-auto" />
                      
                      {/* 装饰边框 */}
                      <div 
                        className="absolute -inset-4 opacity-20 rounded-3xl"
                        style={{
                          backgroundImage: 'url(/images/nft-card-decoration.webp)',
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                        }}
                      ></div>
                    </div>

                    {/* NFT特性 */}
                    <div className="mt-8 space-y-4">
                      <h4 className="text-lg font-semibold text-white">{t('mint.benefits.title')}</h4>
                      <div className="space-y-2">
                        {(selectedTier === 'whitelist' ? tArray('mint.whitelist.benefits') : tArray('mint.student.benefits')).map((benefit: string, index: number) => (
                          <div key={index} className="flex items-center text-white/80">
                            <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {benefit}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 侧边装饰 */}
                <div 
                  className="absolute -left-20 top-1/2 transform -translate-y-1/2 w-40 h-80 opacity-10 pointer-events-none"
                  style={{
                    backgroundImage: 'url(/images/mint-sidebar-decoration.webp)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                ></div>
              </div>

              {/* 右侧 - 铸造界面 */}
              <div className="space-y-8">
                {/* 主要铸造卡片 */}
                <div className="relative group">
                  {/* 发光效果 */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/50 via-pink-500/50 to-cyan-500/50 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-1000"></div>

                  <div className="relative bg-black/60 backdrop-blur-xl rounded-2xl p-8 border border-white/20">
                    <div className="text-center mb-8">
                      <h2 className="text-2xl font-bold text-white mb-2">{t('mint.title')}</h2>
                      <p className="text-white/60">{t('mint.description')}</p>
                    </div>

                    {/* Mint Section */}
                    <MintSection
                      tier={selectedTier}
                      whitelistData={whitelistData}
                      studentData={studentData}
                      isLoading={isLoading}
                    />
                  </div>
                </div>

                {/* 额外信息卡片 */}
                <div className="relative">
                  <div className="bg-black/40 backdrop-blur-xl rounded-2xl p-6 border border-white/10">
                    <h3 className="text-lg font-semibold text-white mb-4">{t('mint.info.title')}</h3>
                    <div className="space-y-3 text-sm text-white/70">
                      <div className="flex justify-between">
                        <span>{t('mint.info.network')}</span>
                        <span className="text-green-400">Ethereum</span>
                      </div>
                      <div className="flex justify-between">
                        <span>{t('mint.info.standard')}</span>
                        <span className="text-blue-400">ERC-721</span>
                      </div>
                      <div className="flex justify-between">
                        <span>{t('mint.info.supply')}</span>
                        <span className="text-purple-400">{t('mint.info.limited')}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 底部说明 */}
            <div className="text-center mt-16">
              <p className="text-white/50 text-sm max-w-2xl mx-auto">
                {t('mint.disclaimer')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
