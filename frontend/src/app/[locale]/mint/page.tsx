'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { MintSection } from '@/components/nft/MintSection';
import { NFTPreview } from '@/components/nft/NFTPreview';
import { useWhitelist } from '@/hooks/useWhitelist';
import { Logo } from '@/components/ui/Logo';
import { useTranslation } from '@/components/providers/I18nProvider';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const { whitelistData, studentData, isLoading } = useWhitelist();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');
  const { t, tArray } = useTranslation();

  return (
    <div className="min-h-screen bg-black overflow-hidden relative">
      {/* 高端Web3背景系统 */}
      <div className="fixed inset-0 z-0">
        {/* 主背景 */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-80"
          style={{
            backgroundImage: 'url(/images/premium-mint-background.webp)',
          }}
        />

        {/* 高级渐变叠加 */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-purple-900/40 to-black/80"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-cyan-500/10 to-purple-500/20"></div>

        {/* 动态光效粒子 */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/15 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>

        {/* 网格效果 */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      {/* 高级Header */}
      <header className="relative z-20 flex justify-between items-center p-8 bg-black/20 backdrop-blur-2xl border-b border-white/10">
        <div className="flex items-center space-x-4">
          <Logo size="lg" showText={true} href="/" />
          <div className="hidden md:block w-px h-8 bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
          <div className="hidden md:block">
            <span className="text-white/60 text-sm font-medium">EXCLUSIVE MINT</span>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/5 rounded-full border border-white/10">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-white/80 text-sm font-medium">Live</span>
          </div>
          <WalletButton />
        </div>
      </header>

      {/* 主要内容区域 - 全新高级布局 */}
      <div className="relative z-10 min-h-screen pt-8">
        <div className="container mx-auto px-8">
          <div className="max-w-8xl mx-auto">

            {/* 顶部英雄区域 */}
            <div className="text-center mb-20">
              <div className="relative inline-block">
                {/* 多层发光效果 */}
                <div className="absolute -inset-8 bg-gradient-to-r from-purple-600/30 via-cyan-600/30 to-pink-600/30 blur-3xl animate-pulse"></div>
                <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/40 via-cyan-500/40 to-pink-500/40 blur-2xl"></div>

                <h1 className="relative text-6xl md:text-8xl font-black text-white mb-8 tracking-tight">
                  <span className="bg-gradient-to-r from-purple-300 via-cyan-300 to-pink-300 bg-clip-text text-transparent">
                    AKASHA
                  </span>
                  <br />
                  <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent">
                    MINT
                  </span>
                </h1>
              </div>

              <div className="max-w-3xl mx-auto mb-12">
                <p className="text-2xl text-white/90 mb-6 font-light leading-relaxed">
                  {t('mint.subtitle')}
                </p>
                <p className="text-lg text-white/70 leading-relaxed">
                  {t('mint.description')}
                </p>
              </div>
            </div>

            {/* 层级选择器 - 高级设计 */}
            <div className="flex justify-center mb-16">
              <div className="relative p-2 bg-black/40 backdrop-blur-xl rounded-3xl border border-white/20">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSelectedTier('whitelist')}
                    className={`relative px-12 py-6 rounded-2xl font-bold text-lg transition-all duration-500 ${
                      selectedTier === 'whitelist'
                        ? 'bg-gradient-to-r from-purple-500 via-purple-600 to-blue-600 text-white shadow-2xl shadow-purple-500/50'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    {selectedTier === 'whitelist' && (
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-purple-600 to-blue-600 rounded-2xl blur-xl opacity-60"></div>
                    )}
                    <span className="relative z-10">{t('mint.whitelist.title')}</span>
                  </button>
                  <button
                    onClick={() => setSelectedTier('student')}
                    className={`relative px-12 py-6 rounded-2xl font-bold text-lg transition-all duration-500 ${
                      selectedTier === 'student'
                        ? 'bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 text-white shadow-2xl shadow-cyan-500/50'
                        : 'text-white/70 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    {selectedTier === 'student' && (
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 rounded-2xl blur-xl opacity-60"></div>
                    )}
                    <span className="relative z-10">{t('mint.student.title')}</span>
                  </button>
                </div>
              </div>
            </div>

            {/* 主要布局 - 高级三栏设计 */}
            <div className="grid lg:grid-cols-3 gap-8 max-w-8xl mx-auto">
              {/* 左侧 - NFT预览展示 */}
              <div className="relative">
                <div className="sticky top-8">
                  {/* NFT展示卡片 - 超高级设计 */}
                  <div className="relative group">
                    {/* 外层发光效果 */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/40 via-cyan-500/40 to-pink-500/40 rounded-3xl blur-2xl opacity-50 group-hover:opacity-70 transition-all duration-1000"></div>

                    {/* 主卡片 */}
                    <div className="relative bg-black/60 backdrop-blur-2xl rounded-3xl p-8 border border-white/30 group-hover:border-white/50 transition-all duration-500">
                      {/* NFT预览区域 */}
                      <div className="relative mb-8">
                        <div className="relative group/nft">
                          {/* NFT背景装饰 */}
                          <div
                            className="absolute -inset-8 opacity-20 rounded-3xl"
                            style={{
                              backgroundImage: 'url(/images/premium-nft-showcase.webp)',
                              backgroundSize: 'cover',
                              backgroundPosition: 'center',
                            }}
                          ></div>

                          {/* NFT发光框架 */}
                          <div className={`absolute -inset-6 bg-gradient-to-r ${selectedTier === 'whitelist' ? 'from-purple-500/30 to-blue-500/30' : 'from-blue-500/30 to-cyan-500/30'} rounded-3xl blur-xl opacity-60 group-hover/nft:opacity-80 transition-all duration-500`}></div>

                          <div className="relative p-6 bg-black/40 backdrop-blur-xl rounded-2xl border border-white/20">
                            <NFTPreview tier={selectedTier} className="w-full transform group-hover/nft:scale-105 transition-transform duration-500" />
                          </div>
                        </div>
                      </div>

                      {/* 层级信息 */}
                      <div className="text-center mb-6">
                        <h3 className="text-2xl font-bold text-white mb-2">
                          <span className={`bg-gradient-to-r ${selectedTier === 'whitelist' ? 'from-purple-300 to-blue-300' : 'from-blue-300 to-cyan-300'} bg-clip-text text-transparent`}>
                            {selectedTier === 'whitelist' ? t('mint.whitelist.title') : t('mint.student.title')}
                          </span>
                        </h3>
                        <p className="text-white/70 text-lg">
                          {selectedTier === 'whitelist' ? t('mint.whitelist.description') : t('mint.student.description')}
                        </p>
                      </div>

                      {/* 价格显示 */}
                      <div className="text-center p-6 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 mb-6">
                        <div className="text-3xl font-bold text-white mb-2">
                          <span className={`bg-gradient-to-r ${selectedTier === 'whitelist' ? 'from-purple-400 to-blue-400' : 'from-blue-400 to-cyan-400'} bg-clip-text text-transparent`}>
                            {selectedTier === 'whitelist' ? '0.08 ETH' : '0.05 ETH'}
                          </span>
                        </div>
                        <p className="text-white/60 text-sm">Mint Price</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 中间栏 - 权益展示 */}
              <div className="relative">
                <div className="sticky top-8 space-y-6">
                  {/* 权益标题 */}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-4">
                      <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                        {t('mint.benefits.title')}
                      </span>
                    </h3>
                  </div>

                  {/* 权益列表 - 高级卡片设计 */}
                  <div className="space-y-4">
                    {(selectedTier === 'whitelist' ? tArray('mint.whitelist.benefits') : tArray('mint.student.benefits')).map((benefit: string, index: number) => (
                      <div key={index} className="group relative">
                        {/* 发光效果 */}
                        <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/20 via-cyan-500/20 to-pink-500/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                        <div className="relative p-4 bg-black/40 backdrop-blur-xl rounded-xl border border-white/20 group-hover:border-white/40 transition-all duration-300">
                          <div className="flex items-center">
                            <div className={`w-8 h-8 rounded-full ${selectedTier === 'whitelist' ? 'bg-gradient-to-r from-purple-400 to-blue-400' : 'bg-gradient-to-r from-blue-400 to-cyan-400'} flex items-center justify-center mr-4 flex-shrink-0`}>
                              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <span className="text-white/90 font-medium text-sm leading-relaxed">{benefit}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 稀有度指示器 */}
                  <div className="relative group">
                    <div className="absolute -inset-2 bg-gradient-to-r from-yellow-500/30 via-orange-500/30 to-red-500/30 rounded-2xl blur-xl opacity-50 group-hover:opacity-70 transition-all duration-1000"></div>

                    <div className="relative p-6 bg-black/50 backdrop-blur-xl rounded-2xl border border-yellow-500/30">
                      <div className="text-center">
                        <div className="text-lg font-bold text-white mb-2">
                          <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                            稀有度等级
                          </span>
                        </div>
                        <div className="flex justify-center items-center space-x-1 mb-3">
                          {[...Array(selectedTier === 'whitelist' ? 5 : 3)].map((_, i) => (
                            <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                        </div>
                        <p className="text-white/60 text-sm">
                          {selectedTier === 'whitelist' ? '传奇级别' : '稀有级别'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 右侧 - 铸造控制台 */}
              <div className="relative">
                <div className="sticky top-8 space-y-6">
                  {/* 主要铸造卡片 - 超高级设计 */}
                  <div className="relative group">
                    {/* 外层发光效果 */}
                    <div className="absolute -inset-4 bg-gradient-to-r from-cyan-500/40 via-purple-500/40 to-pink-500/40 rounded-3xl blur-2xl opacity-50 group-hover:opacity-70 transition-all duration-1000"></div>

                    {/* 主卡片 */}
                    <div className="relative bg-black/60 backdrop-blur-2xl rounded-3xl p-8 border border-white/30 group-hover:border-white/50 transition-all duration-500">
                      {/* 铸造标题 */}
                      <div className="text-center mb-8">
                        <div className="relative">
                          <div className="absolute inset-0 bg-gradient-to-r from-cyan-600/20 via-purple-600/20 to-pink-600/20 blur-2xl"></div>
                          <h2 className="relative text-3xl font-bold text-white mb-3">
                            <span className="bg-gradient-to-r from-cyan-300 via-purple-300 to-pink-300 bg-clip-text text-transparent">
                              {t('mint.title')}
                            </span>
                          </h2>
                        </div>
                        <p className="text-white/80 text-lg">{t('mint.description')}</p>
                      </div>

                      {/* Mint Section */}
                      <MintSection
                        tier={selectedTier}
                        whitelistData={whitelistData}
                        studentData={studentData}
                        isLoading={isLoading}
                      />
                    </div>
                  </div>

                  {/* 技术信息卡片 - 高级设计 */}
                  <div className="relative group">
                    <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-cyan-500/30 rounded-2xl blur-xl opacity-40 group-hover:opacity-60 transition duration-1000"></div>

                    <div className="relative bg-black/50 backdrop-blur-xl rounded-2xl p-6 border border-white/20 group-hover:border-white/40 transition-all duration-300">
                      <h3 className="text-lg font-bold text-white mb-6 text-center">
                        <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                          技术规格
                        </span>
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                          <span className="text-white/80 font-medium text-sm">网络</span>
                          <span className="text-green-400 font-bold flex items-center text-sm">
                            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                            Ethereum
                          </span>
                        </div>
                        <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                          <span className="text-white/80 font-medium text-sm">标准</span>
                          <span className="text-blue-400 font-bold text-sm">ERC-721</span>
                        </div>
                        <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                          <span className="text-white/80 font-medium text-sm">供应量</span>
                          <span className="text-purple-400 font-bold text-sm">限量版</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 实时统计 */}
                  <div className="relative group">
                    <div className="absolute -inset-2 bg-gradient-to-r from-green-500/30 via-emerald-500/30 to-teal-500/30 rounded-2xl blur-xl opacity-40 group-hover:opacity-60 transition duration-1000"></div>

                    <div className="relative bg-black/50 backdrop-blur-xl rounded-2xl p-6 border border-white/20 group-hover:border-white/40 transition-all duration-300">
                      <h3 className="text-lg font-bold text-white mb-6 text-center">
                        <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                          实时数据
                        </span>
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                          <span className="text-white/80 font-medium text-sm">已铸造</span>
                          <span className="text-green-400 font-bold text-sm">1,247 / 10,000</span>
                        </div>
                        <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                          <span className="text-white/80 font-medium text-sm">持有者</span>
                          <span className="text-emerald-400 font-bold text-sm">892</span>
                        </div>
                        <div className="flex justify-between items-center p-3 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10">
                          <span className="text-white/80 font-medium text-sm">地板价</span>
                          <span className="text-teal-400 font-bold text-sm">0.12 ETH</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 底部高级信息区域 */}
            <div className="mt-24 space-y-12">
              {/* 安全提示 */}
              <div className="text-center">
                <div className="relative max-w-5xl mx-auto">
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-500/20 via-orange-500/20 to-red-500/20 rounded-3xl blur-2xl opacity-50"></div>
                  <div className="relative bg-black/50 backdrop-blur-2xl rounded-3xl p-8 border border-amber-500/30">
                    <div className="flex items-center justify-center mb-6">
                      <div className="w-12 h-12 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full flex items-center justify-center mr-4">
                        <svg className="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h4 className="text-2xl font-bold text-white">
                        <span className="bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
                          安全提示
                        </span>
                      </h4>
                    </div>
                    <p className="text-white/80 text-lg leading-relaxed max-w-3xl mx-auto">
                      {t('mint.disclaimer')}
                    </p>
                  </div>
                </div>
              </div>

              {/* 社区链接 */}
              <div className="text-center">
                <div className="relative max-w-4xl mx-auto">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-cyan-500/20 rounded-3xl blur-2xl opacity-50"></div>
                  <div className="relative bg-black/50 backdrop-blur-2xl rounded-3xl p-8 border border-white/20">
                    <h4 className="text-xl font-bold text-white mb-6">
                      <span className="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                        加入 AKASHA 社区
                      </span>
                    </h4>
                    <div className="flex justify-center space-x-6">
                      <a href="#" className="group relative p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-white/30 transition-all duration-300">
                        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <span className="relative text-white/80 group-hover:text-white font-medium">Discord</span>
                      </a>
                      <a href="#" className="group relative p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-white/30 transition-all duration-300">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <span className="relative text-white/80 group-hover:text-white font-medium">Twitter</span>
                      </a>
                      <a href="#" className="group relative p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-white/30 transition-all duration-300">
                        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-teal-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <span className="relative text-white/80 group-hover:text-white font-medium">OpenSea</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
