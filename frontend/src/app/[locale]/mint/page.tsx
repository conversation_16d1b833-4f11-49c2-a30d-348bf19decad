'use client';

import { useState } from 'react';
import { useAccount } from 'wagmi';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Logo } from '@/components/ui/Logo';
import { useTranslation } from '@/components/providers/I18nProvider';

export default function MintPage() {
  const { address, isConnected } = useAccount();
  const [selectedTier, setSelectedTier] = useState<'whitelist' | 'student'>('whitelist');
  const [mintAmount, setMintAmount] = useState(1);
  const { t } = useTranslation();

  // 六边形样式对象
  const hexagonStyles = {
    large: {
      width: '400px',
      height: '346px',
      position: 'relative' as const,
      clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    },
    medium: {
      width: '200px',
      height: '173px',
      position: 'relative' as const,
      clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    },
    small: {
      width: '120px',
      height: '104px',
      position: 'relative' as const,
      clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
    },
  };

  return (
    <div className="min-h-screen bg-black overflow-hidden relative">

      {/* 六边形Web3背景系统 */}
      <div className="fixed inset-0 z-0">
        {/* 主背景 */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-70"
          style={{
            backgroundImage: 'url(/images/hexagonal-mint-interface.webp)',
          }}
        />

        {/* 六边形网格叠加 */}
        <div className="absolute inset-0 opacity-20">
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="hexGrid" x="0" y="0" width="100" height="87" patternUnits="userSpaceOnUse">
                <polygon points="50,1 95,25 95,75 50,99 5,75 5,25" fill="none" stroke="#8b5cf6" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#hexGrid)" />
          </svg>
        </div>

        {/* 高级渐变叠加 */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-purple-900/30 to-black/70" />
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-cyan-900/20 to-transparent" />
      </div>

      {/* 高级Header */}
      <header className="relative z-20 flex justify-between items-center p-8 bg-black/20 backdrop-blur-2xl border-b border-white/10">
        <div className="flex items-center space-x-4">
          <Logo size="lg" showText={true} href="/" />
          <div className="hidden md:block w-px h-8 bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
          <div className="hidden md:block">
            <span className="text-white/60 text-sm font-medium">HEXAGONAL MINT</span>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/5 rounded-full border border-white/10">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-white/80 text-sm font-medium">Live</span>
          </div>
          <WalletButton />
        </div>
      </header>

      {/* 主要内容 - 革命性六边形蜂巢布局 */}
      <main className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-20">
        <div className="max-w-6xl mx-auto w-full">

          {/* 六边形蜂巢容器 */}
          <div className="relative flex items-center justify-center min-h-[80vh]">

            {/* 中心核心六边形 - 主要铸造区域 */}
            <div className="relative group">
              <div
                style={hexagonStyles.large}
                className="bg-black/70 backdrop-blur-2xl border border-purple-500/50 group-hover:border-purple-500/80 transition-all duration-500 flex items-center justify-center"
              >
                <div className="absolute -inset-8 bg-gradient-to-r from-purple-600/30 via-cyan-600/30 to-pink-600/30 blur-3xl animate-pulse"></div>
                <div className="relative text-center p-8">
                  <h1 className="text-4xl font-black text-white mb-4">
                    <span className="bg-gradient-to-r from-purple-300 via-cyan-300 to-pink-300 bg-clip-text text-transparent">
                      AKASHA MINT
                    </span>
                  </h1>
                  <p className="text-white/80 text-lg mb-6">铸造你的神秘身份</p>

                  {/* 铸造数量选择器 */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center gap-4 mb-4">
                      <button
                        onClick={() => setMintAmount(Math.max(1, mintAmount - 1))}
                        className="w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center text-white font-bold text-xl transition-colors"
                      >
                        -
                      </button>
                      <span className="text-3xl font-bold text-white min-w-[60px]">{mintAmount}</span>
                      <button
                        onClick={() => setMintAmount(Math.min(5, mintAmount + 1))}
                        className="w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center text-white font-bold text-xl transition-colors"
                      >
                        +
                      </button>
                    </div>
                    <p className="text-white/60 text-sm">最多可铸造 5 个</p>
                  </div>

                  {/* 价格显示 */}
                  <div className="mb-8">
                    <div className="text-3xl font-bold text-white mb-2">{(0.08 * mintAmount).toFixed(2)} ETH</div>
                    <div className="text-white/60 text-sm">≈ ${(200 * mintAmount).toFixed(0)} USD</div>
                  </div>

                  {/* 铸造按钮 */}
                  <button className="w-full px-8 py-4 bg-gradient-to-r from-purple-500 via-cyan-500 to-pink-500 hover:from-purple-600 hover:via-cyan-600 hover:to-pink-600 text-white font-bold rounded-2xl transition-all duration-500 transform hover:scale-105 shadow-2xl shadow-purple-500/50">
                    立即铸造
                  </button>
                </div>
              </div>
            </div>

            {/* 周围的六边形卫星 */}
            {/* 顶部六边形 - NFT预览 */}
            <div className="absolute -top-32 left-1/2 transform -translate-x-1/2 group">
              <div
                style={hexagonStyles.medium}
                className="bg-black/60 backdrop-blur-xl border border-cyan-500/40 group-hover:border-cyan-500/70 transition-all duration-500 flex items-center justify-center"
              >
                <div className="relative text-center p-6">
                  <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-4 border-2 border-cyan-400">
                    <img
                      src="/images/premium-nft-showcase.webp"
                      alt="NFT Preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h3 className="text-white font-bold text-lg mb-2">NFT预览</h3>
                  <p className="text-cyan-300 text-sm">Genesis #001</p>
                </div>
              </div>
            </div>

            {/* 右上六边形 - 稀有度 */}
            <div className="absolute -top-16 right-8 group">
              <div
                style={hexagonStyles.small}
                className="bg-black/60 backdrop-blur-xl border border-yellow-500/40 group-hover:border-yellow-500/70 transition-all duration-500 flex items-center justify-center"
              >
                <div className="text-center p-4">
                  <div className="text-2xl font-bold text-yellow-400 mb-1">RARE</div>
                  <div className="text-yellow-300 text-xs">稀有度</div>
                </div>
              </div>
            </div>

            {/* 右下六边形 - 权益 */}
            <div className="absolute bottom-16 right-8 group">
              <div
                style={hexagonStyles.small}
                className="bg-black/60 backdrop-blur-xl border border-pink-500/40 group-hover:border-pink-500/70 transition-all duration-500 flex items-center justify-center"
              >
                <div className="text-center p-4">
                  <div className="text-2xl font-bold text-pink-400 mb-1">DAO</div>
                  <div className="text-pink-300 text-xs">治理权益</div>
                </div>
              </div>
            </div>

            {/* 底部六边形 - 统计 */}
            <div className="absolute -bottom-32 left-1/2 transform -translate-x-1/2 group">
              <div
                style={hexagonStyles.medium}
                className="bg-black/60 backdrop-blur-xl border border-green-500/40 group-hover:border-green-500/70 transition-all duration-500 flex items-center justify-center"
              >
                <div className="text-center p-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-xl font-bold text-white">3,247</div>
                      <div className="text-green-300 text-xs">已铸造</div>
                    </div>
                    <div>
                      <div className="text-xl font-bold text-white">6,753</div>
                      <div className="text-green-300 text-xs">剩余</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 左下六边形 - 社区 */}
            <div className="absolute bottom-16 left-8 group">
              <div
                style={hexagonStyles.small}
                className="bg-black/60 backdrop-blur-xl border border-blue-500/40 group-hover:border-blue-500/70 transition-all duration-500 flex items-center justify-center"
              >
                <div className="text-center p-4">
                  <div className="text-2xl font-bold text-blue-400 mb-1">3K+</div>
                  <div className="text-blue-300 text-xs">社区成员</div>
                </div>
              </div>
            </div>

            {/* 左上六边形 - 价格 */}
            <div className="absolute -top-16 left-8 group">
              <div
                style={hexagonStyles.small}
                className="bg-black/60 backdrop-blur-xl border border-purple-500/40 group-hover:border-purple-500/70 transition-all duration-500 flex items-center justify-center"
              >
                <div className="text-center p-4">
                  <div className="text-lg font-bold text-purple-400 mb-1">0.08</div>
                  <div className="text-purple-300 text-xs">ETH</div>
                </div>
              </div>
            </div>

            {/* 连接线动画 */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 800 600">
              <defs>
                <linearGradient id="hexGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.3" />
                  <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.3" />
                  <stop offset="100%" stopColor="#ec4899" stopOpacity="0.3" />
                </linearGradient>
              </defs>
              <path
                d="M400,300 L400,150 M400,300 L550,225 M400,300 L550,375 M400,300 L400,450 M400,300 L250,375 M400,300 L250,225"
                fill="none"
                stroke="url(#hexGradient)"
                strokeWidth="2"
                strokeDasharray="5,5"
                className="animate-pulse"
              />
            </svg>

          </div>
        </div>
      </main>
    </div>
  );
}
