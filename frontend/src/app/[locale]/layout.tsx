import { ReactNode } from 'react';
import { notFound } from 'next/navigation';
import { I18nProvider } from '@/components/providers/I18nProvider';

const locales = ['en', 'zh'];

interface LocaleLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

// 加载翻译文件
async function getTranslations(locale: string) {
  try {
    const translations = await import(`../../../public/locales/${locale}/common.json`);
    return translations.default;
  } catch (error) {
    console.error(`Failed to load translations for locale: ${locale}`, error);
    return {};
  }
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = params;

  // 验证语言代码
  if (!locales.includes(locale)) {
    notFound();
  }

  const translations = await getTranslations(locale);

  return (
    <I18nProvider locale={locale} translations={translations}>
      {children}
    </I18nProvider>
  );
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
