'use client';

import { useState } from 'react';
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useReadContract } from 'wagmi';
import { parseEther } from 'viem';
import { CONTRACT_ADDRESSES, AKASHA_NFT_ABI, PRICES, NFTTier } from '@/lib/web3';
import { useWhitelistStatus } from '@/hooks/useWhitelist';

interface MintCardProps {
  tier: 'whitelist' | 'lottery' | 'student';
  title: string;
  description: string;
  price: string;
  benefits: string[];
  onMintSuccess?: (tokenId: number) => void;
}

export function MintCard({
  tier,
  title,
  description,
  price,
  benefits,
  onMintSuccess,
}: MintCardProps) {
  const { address, isConnected } = useAccount();
  const [isMinting, setIsMinting] = useState(false);

  // 获取白名单状态
  const whitelistType = tier === 'student' ? 'student' : 'whitelist';
  const { isEligible, merkleProof, isLoading: whitelistLoading } = useWhitelistStatus(whitelistType);

  // 读取销售状态
  const { data: saleActive } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: `${tier}SaleActive`,
  });

  // 读取剩余供应量
  const { data: remainingSupply } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: tier === 'whitelist' ? 'remainingWhitelistSupply' : 
                  tier === 'lottery' ? 'remainingLotterySupply' : 
                  'remainingStudentSupply',
  });

  // 写入合约
  const { writeContract, data: hash, error } = useWriteContract();

  // 等待交易确认
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const handleMint = async () => {
    if (!isConnected || !address || !isEligible || !saleActive) return;

    try {
      setIsMinting(true);
      
      const functionName = `${tier}Mint`;
      const value = parseEther(price);

      await writeContract({
        address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
        abi: AKASHA_NFT_ABI,
        functionName,
        args: [merkleProof],
        value,
      });
    } catch (err) {
      console.error('Mint failed:', err);
    } finally {
      setIsMinting(false);
    }
  };

  const isLoading = isMinting || isConfirming || whitelistLoading;
  const canMint = isConnected && isEligible && saleActive && !isLoading;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
      {/* 卡片头部 */}
      <div className="bg-gradient-to-r from-amber-500 to-amber-600 p-6 text-white">
        <h3 className="text-2xl font-bold mb-2">{title}</h3>
        <p className="text-amber-100">{description}</p>
      </div>

      {/* 卡片内容 */}
      <div className="p-6">
        {/* 价格显示 */}
        <div className="mb-6">
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {price} ETH
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            剩余供应量: {remainingSupply?.toString() || '0'}
          </div>
        </div>

        {/* 权益列表 */}
        <div className="mb-6">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
            专属权益
          </h4>
          <ul className="space-y-2">
            {benefits.map((benefit, index) => (
              <li key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <svg
                  className="w-4 h-4 text-green-500 mr-2 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        {/* 状态提示 */}
        {!isConnected && (
          <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              请先连接钱包
            </p>
          </div>
        )}

        {isConnected && !isEligible && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-800 dark:text-red-200">
              您不在{title}名单中
            </p>
          </div>
        )}

        {isConnected && !saleActive && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-lg">
            <p className="text-sm text-gray-800 dark:text-gray-200">
              {title}销售暂未开始
            </p>
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-800 dark:text-red-200">
              交易失败: {error.message}
            </p>
          </div>
        )}

        {isSuccess && (
          <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <p className="text-sm text-green-800 dark:text-green-200">
              NFT铸造成功！
            </p>
          </div>
        )}

        {/* Mint按钮 */}
        <button
          onClick={handleMint}
          disabled={!canMint}
          className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
            canMint
              ? 'bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
              : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              {isMinting ? '铸造中...' : '确认中...'}
            </div>
          ) : (
            `铸造 ${title} NFT`
          )}
        </button>
      </div>
    </div>
  );
}
