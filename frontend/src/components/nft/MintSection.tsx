'use client';

import { useState } from 'react';
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useReadContract } from 'wagmi';
import { parseEther, formatEther } from 'viem';
import { CONTRACT_ADDRESSES, AKASHA_NFT_ABI, PRICES, NFTTier } from '@/lib/web3';
import { useTranslation } from '@/hooks/useTranslation';

interface WhitelistData {
  address: string;
  type: 'whitelist' | 'student';
  isEligible: boolean;
  merkleProof: string[];
  merkleRoot: string;
}

interface MintSectionProps {
  tier: 'whitelist' | 'student';
  whitelistData: WhitelistData | null;
  studentData: WhitelistData | null;
  isLoading: boolean;
}

export function MintSection({ tier, whitelistData, studentData, isLoading }: MintSectionProps) {
  const { address, isConnected } = useAccount();
  const [mintAmount, setMintAmount] = useState(1);
  const [isMinting, setIsMinting] = useState(false);
  const { t } = useTranslation();

  const currentData = tier === 'whitelist' ? whitelistData : studentData;
  const price = tier === 'whitelist' ? PRICES.WHITELIST : PRICES.STUDENT;
  const totalPrice = parseEther((parseFloat(price) * mintAmount).toString());

  // 读取合约状态
  const { data: saleActive } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: 'saleActive',
  });

  const { data: totalSupply } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: 'totalSupply',
  });

  const { data: maxSupply } = useReadContract({
    address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
    abi: AKASHA_NFT_ABI,
    functionName: 'MAX_SUPPLY',
  });

  // 写入合约
  const { writeContract, data: hash, error, isPending } = useWriteContract();

  // 等待交易确认
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const handleMint = async () => {
    if (!isConnected || !address || !currentData?.isEligible) return;

    setIsMinting(true);
    try {
      const nftTier = tier === 'whitelist' ? NFTTier.WHITELIST : NFTTier.STUDENT;
      
      if (tier === 'whitelist') {
        writeContract({
          address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
          abi: AKASHA_NFT_ABI,
          functionName: 'whitelistMint',
          args: [currentData.merkleProof, nftTier, mintAmount],
          value: totalPrice,
        });
      } else {
        writeContract({
          address: CONTRACT_ADDRESSES.AKASHA_NFT as `0x${string}`,
          abi: AKASHA_NFT_ABI,
          functionName: 'studentMint',
          args: [currentData.merkleProof, mintAmount],
          value: totalPrice,
        });
      }
    } catch (err) {
      console.error('Mint error:', err);
    } finally {
      setIsMinting(false);
    }
  };

  const canMint = isConnected && currentData?.isEligible && saleActive && !isLoading;
  const buttonLoading = isMinting || isPending || isConfirming;

  return (
    <div className="space-y-6">
      {/* Supply Information */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-1">{t('mint.supply')}:</label>
          <div className="text-lg font-bold text-white">
            {totalSupply?.toString() || '0'} / {maxSupply?.toString() || '3000'}
          </div>
          <div className="text-xs text-slate-400">[{t('mint.minted')}]</div>
        </div>
        <div>
          <label className="block text-sm font-medium text-slate-300 mb-1">{t('mint.amount')}:</label>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setMintAmount(Math.max(1, mintAmount - 1))}
              className="w-8 h-8 bg-slate-700 hover:bg-slate-600 rounded-lg flex items-center justify-center text-white transition-colors"
              disabled={mintAmount <= 1}
            >
              -
            </button>
            <span className="text-lg font-bold text-white w-8 text-center">{mintAmount}</span>
            <button
              onClick={() => setMintAmount(Math.min(5, mintAmount + 1))}
              className="w-8 h-8 bg-slate-700 hover:bg-slate-600 rounded-lg flex items-center justify-center text-white transition-colors"
              disabled={mintAmount >= 5}
            >
              +
            </button>
          </div>
        </div>
      </div>

      {/* Total Price */}
      <div>
        <label className="block text-sm font-medium text-slate-300 mb-1">Total:</label>
        <div className="text-2xl font-bold text-white">
          {parseFloat(formatEther(totalPrice)).toFixed(4)} ETH
        </div>
      </div>

      {/* Eligibility Status */}
      {isConnected && (
        <div className="p-4 rounded-lg border border-slate-600 bg-slate-700/50">
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">
              {tier === 'whitelist' ? 'Whitelist' : 'Student'} Status:
            </span>
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm text-blue-400">检查中...</span>
              </div>
            ) : currentData?.isEligible ? (
              <span className="text-sm text-green-400 font-medium">✅ 已验证</span>
            ) : (
              <span className="text-sm text-red-400 font-medium">❌ 未授权</span>
            )}
          </div>
        </div>
      )}

      {/* Mint Buttons */}
      <div className="space-y-3">
        {!isConnected ? (
          <div className="text-center py-4">
            <p className="text-slate-400 mb-4">{t('mint.connectWallet')}</p>
          </div>
        ) : !currentData?.isEligible ? (
          <button
            disabled
            className="w-full py-4 bg-slate-600 text-slate-400 rounded-xl font-bold cursor-not-allowed"
          >
{t('mint.notEligible')}
          </button>
        ) : (
          <>
            {/* Mint & Stake Button */}
            <button
              onClick={handleMint}
              disabled={!canMint || buttonLoading}
              className={`w-full py-4 rounded-xl font-bold text-white transition-all ${
                canMint && !buttonLoading
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 transform hover:scale-105'
                  : 'bg-slate-600 cursor-not-allowed'
              }`}
            >
              {buttonLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>
{isPending ? t('mint.confirming') : isConfirming ? t('mint.minting') : t('mint.processing')}
                  </span>
                </div>
              ) : (
t('mint.mintNow')
              )}
            </button>

            {/* Simple Mint Button */}
            <button
              onClick={handleMint}
              disabled={!canMint || buttonLoading}
              className={`w-full py-4 rounded-xl font-bold transition-all ${
                canMint && !buttonLoading
                  ? 'bg-slate-700 hover:bg-slate-600 text-white'
                  : 'bg-slate-600 cursor-not-allowed text-slate-400'
              }`}
            >
              {buttonLoading ? '处理中...' : 'MINT'}
            </button>
          </>
        )}
      </div>

      {/* Transaction Status */}
      {error && (
        <div className="p-4 bg-red-900/50 border border-red-500 rounded-lg">
          <p className="text-red-400 text-sm">
            交易失败: {error.message}
          </p>
        </div>
      )}

      {isSuccess && (
        <div className="p-4 bg-green-900/50 border border-green-500 rounded-lg">
          <p className="text-green-400 text-sm">
            🎉 铸造成功！NFT已添加到您的钱包中。
          </p>
        </div>
      )}

      {hash && (
        <div className="p-4 bg-blue-900/50 border border-blue-500 rounded-lg">
          <p className="text-blue-400 text-sm">
            交易哈希: <span className="font-mono break-all">{hash}</span>
          </p>
        </div>
      )}
    </div>
  );
}
