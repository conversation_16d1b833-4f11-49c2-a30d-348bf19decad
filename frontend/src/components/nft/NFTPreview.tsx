'use client';

import Image from 'next/image';

interface NFTPreviewProps {
  tier: 'whitelist' | 'student';
  className?: string;
}

export function NFTPreview({ tier, className = '' }: NFTPreviewProps) {
  const isWhitelist = tier === 'whitelist';

  return (
    <div className={`relative aspect-square rounded-2xl overflow-hidden ${className}`}>
      {/* Generated NFT Image */}
      <div className="absolute inset-0">
        <Image
          src={isWhitelist ? '/images/akasha-nft-whitelist.png' : '/images/akasha-nft-student.png'}
          alt={`AkashaDao ${tier} NFT`}
          fill
          className="object-cover"
          priority
        />
      </div>

      {/* Overlay Effects */}
      <div className={`absolute inset-0 ${
        isWhitelist
          ? 'bg-gradient-to-br from-yellow-400/20 via-orange-500/20 to-red-500/20'
          : 'bg-gradient-to-br from-blue-400/20 via-purple-500/20 to-cyan-500/20'
      }`}>
        {/* Subtle Animation Overlay */}
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-50 animate-pulse"></div>

        {/* Interactive Glow Effect */}
        <div className={`absolute inset-0 rounded-2xl transition-all duration-300 hover:shadow-2xl ${
          isWhitelist
            ? 'hover:shadow-orange-500/50'
            : 'hover:shadow-blue-500/50'
        }`}></div>
      </div>
    </div>
  );
}
