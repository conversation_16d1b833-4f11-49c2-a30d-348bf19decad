import React from 'react';
import Link from 'next/link';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  showSubtitle?: boolean;
  href?: string;
  className?: string;
}

export const Logo: React.FC<LogoProps> = ({ 
  size = 'md', 
  showText = true, 
  showSubtitle = false,
  href = '/',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      icon: 'w-8 h-8',
      text: 'text-lg',
      subtitle: 'text-xs'
    },
    md: {
      icon: 'w-10 h-10',
      text: 'text-xl',
      subtitle: 'text-xs'
    },
    lg: {
      icon: 'w-12 h-12',
      text: 'text-2xl',
      subtitle: 'text-sm'
    }
  };

  const LogoContent = () => (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo图标 */}
      <div className={`${sizeClasses[size].icon} bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105`}>
        <span className={`text-white font-bold ${size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-xl' : 'text-lg'}`}>
          A
        </span>
      </div>
      
      {/* 文字部分 */}
      {showText && (
        <div className="flex flex-col">
          <h1 className={`${sizeClasses[size].text} font-bold text-white leading-tight`}>
            AkashaDao
          </h1>
          {showSubtitle && (
            <span className={`${sizeClasses[size].subtitle} text-purple-300 leading-tight`}>
              Community Pass
            </span>
          )}
        </div>
      )}
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="hover:opacity-80 transition-opacity">
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
};

export default Logo;
