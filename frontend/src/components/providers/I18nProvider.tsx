'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface I18nContextType {
  locale: string;
  setLocale: (locale: string) => void;
  t: (key: string) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
  locale: string;
  translations: Record<string, any>;
}

export function I18nProvider({ children, locale, translations }: I18nProviderProps) {
  const [currentLocale, setCurrentLocale] = useState(locale);
  const router = useRouter();
  const pathname = usePathname();

  const t = (key: string): string => {
    const keys = key.split('.');
    let value = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // 返回key作为fallback
      }
    }

    return typeof value === 'string' ? value : key;
  };

  const setLocale = (newLocale: string) => {
    setCurrentLocale(newLocale);

    // 设置cookie
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}`;

    // 更新URL
    const segments = pathname.split('/');
    segments[1] = newLocale; // 替换语言代码
    const newPath = segments.join('/');
    router.push(newPath);
  };

  return (
    <I18nContext.Provider value={{ locale: currentLocale, setLocale, t }}>
      {children}
    </I18nContext.Provider>
  );
}

// 语言切换Hook
export function useLanguage() {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useLanguage must be used within I18nProvider');
  }

  return {
    currentLanguage: context.locale,
    changeLanguage: context.setLocale,
    availableLanguages: ['en', 'zh'],
  };
}

// useTranslation Hook
export function useTranslation() {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useTranslation must be used within I18nProvider');
  }

  return {
    t: context.t,
    i18n: {
      language: context.locale,
      changeLanguage: context.setLocale,
    },
  };
}

// 语言显示名称映射
export const languageNames: Record<string, string> = {
  en: 'English',
  zh: '中文',
};

export default I18nProvider;
