# Dependencies
node_modules/
*/node_modules/

# Build outputs
dist/
build/
*/dist/
*/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Next.js specific
/.next/
/out/
next-env.d.ts
*.tsbuildinfo

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Vercel
.vercel

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Next.js
frontend/.next/
frontend/out/

# Temporary folders
tmp/
temp/
