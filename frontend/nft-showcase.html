<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AkashaDao NFT 展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes glow {
            0% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.3); }
            100% { box-shadow: 0 0 40px rgba(139, 92, 246, 0.6); }
        }
        .animate-float { animation: float 3s ease-in-out infinite; }
        .animate-glow { animation: glow 2s ease-in-out infinite alternate; }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen">
    <!-- Header -->
    <header class="text-center py-12">
        <h1 class="text-4xl font-bold text-white mb-4">AkashaDao Community Pass</h1>
        <p class="text-xl text-slate-300">AI生成的NFT艺术作品展示</p>
    </header>

    <!-- NFT Gallery -->
    <div class="container mx-auto px-6 pb-12">
        <div class="grid md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            
            <!-- Whitelist NFT -->
            <div class="space-y-6">
                <div class="relative aspect-square rounded-2xl overflow-hidden animate-glow">
                    <img 
                        src="public/images/akasha-nft-whitelist.png" 
                        alt="AkashaDao Whitelist NFT" 
                        class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    >
                    <!-- Overlay Effects -->
                    <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-50"></div>
                    
                    <!-- Hover Glow -->
                    <div class="absolute inset-0 rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-orange-500/50"></div>
                </div>
                
                <!-- Info Card -->
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-white">Whitelist Pass</h3>
                        <span class="px-3 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-black text-sm font-bold rounded-full">
                            PREMIUM
                        </span>
                    </div>
                    <div class="space-y-2 text-sm text-slate-300">
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            投票权和治理参与
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                            无限制内容访问
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-red-400 rounded-full mr-3"></span>
                            优先客户支持
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                            独家活动和空投
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-slate-600">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-400">价格:</span>
                            <span class="text-white font-bold">0.05 ETH</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student NFT -->
            <div class="space-y-6">
                <div class="relative aspect-square rounded-2xl overflow-hidden animate-glow">
                    <img 
                        src="public/images/akasha-nft-student.png" 
                        alt="AkashaDao Student NFT" 
                        class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    >
                    <!-- Overlay Effects -->
                    <div class="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent opacity-50"></div>
                    
                    <!-- Hover Glow -->
                    <div class="absolute inset-0 rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/50"></div>
                </div>
                
                <!-- Info Card -->
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-white">Student Pass</h3>
                        <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-sm font-bold rounded-full">
                            STUDENT
                        </span>
                    </div>
                    <div class="space-y-2 text-sm text-slate-300">
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            学习资源优先访问
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                            导师一对一指导
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-cyan-400 rounded-full mr-3"></span>
                            实习和工作推荐
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            技能认证和证书
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-slate-600">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-400">价格:</span>
                            <span class="text-white font-bold">0.02 ETH</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-12 space-y-4">
            <div class="space-x-4">
                <a 
                    href="/mint" 
                    class="inline-block px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl font-bold transition-all transform hover:scale-105 animate-float"
                >
                    开始铸造 NFT
                </a>
                <a 
                    href="test-components.html" 
                    class="inline-block px-8 py-4 bg-slate-700 hover:bg-slate-600 text-white rounded-xl font-bold transition-all"
                >
                    查看完整界面
                </a>
            </div>
            <p class="text-slate-400 text-sm">
                🎨 NFT图片由AI生成 | 💎 基于您的参考设计创建
            </p>
        </div>

        <!-- Technical Info -->
        <div class="mt-16 max-w-2xl mx-auto">
            <div class="bg-slate-800/30 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
                <h4 class="text-lg font-bold text-white mb-4">🛠 技术实现</h4>
                <div class="grid md:grid-cols-2 gap-4 text-sm text-slate-300">
                    <div>
                        <h5 class="font-semibold text-white mb-2">AI图片生成:</h5>
                        <ul class="space-y-1">
                            <li>• Flux-Schnell模型</li>
                            <li>• 1024x1024分辨率</li>
                            <li>• PNG格式输出</li>
                            <li>• 渐变背景设计</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold text-white mb-2">前端集成:</h5>
                        <ul class="space-y-1">
                            <li>• Next.js Image组件</li>
                            <li>• 响应式设计</li>
                            <li>• 动画效果</li>
                            <li>• Web3集成</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
