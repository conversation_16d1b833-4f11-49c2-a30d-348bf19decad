<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>I18n Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
        }
        .success {
            color: #4ade80;
        }
        .error {
            color: #ef4444;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <h1>多语言功能测试 / Multilingual Function Test</h1>
    
    <div class="test-section">
        <h2>翻译文件测试 / Translation Files Test</h2>
        <button onclick="testTranslations()">测试翻译 / Test Translations</button>
        <div id="translation-results"></div>
    </div>

    <div class="test-section">
        <h2>语言切换测试 / Language Switch Test</h2>
        <button onclick="switchToEnglish()">Switch to English</button>
        <button onclick="switchToChinese()">切换到中文</button>
        <div id="current-language">当前语言 / Current Language: <span id="lang-display">zh</span></div>
    </div>

    <div class="test-section">
        <h2>翻译内容预览 / Translation Content Preview</h2>
        <div id="content-preview">
            <h3 id="preview-title">铸造您的通行证</h3>
            <p id="preview-subtitle">专属社区会员通行证</p>
            <p id="preview-description">加入专属的AkashaDao社区</p>
            <ul id="preview-benefits">
                <li>治理投票权</li>
                <li>高级社区访问权限</li>
                <li>新功能抢先体验</li>
                <li>专属活动邀请</li>
            </ul>

            <hr style="margin: 20px 0; border-color: #333;">

            <h3 id="roadmap-title">发展路线图</h3>
            <p>第一季度 2024 计划:</p>
            <ul id="roadmap-items">
                <li>NFT通行证发布</li>
                <li>社区治理启动</li>
                <li>基础功能上线</li>
            </ul>

            <hr style="margin: 20px 0; border-color: #333;">

            <h3 id="community-title">加入我们的社区</h3>
            <p>与志同道合的人建立联系</p>
        </div>
    </div>

    <script>
        // 模拟翻译数据
        const translations = {
            en: {
                "mint": {
                    "title": "Mint Your Pass",
                    "subtitle": "Exclusive Community Membership Pass",
                    "description": "Join the exclusive AkashaDao community",
                    "whitelist": {
                        "benefits": [
                            "Voting rights in governance",
                            "Premium community access",
                            "Early access to new features",
                            "Exclusive events invitation"
                        ]
                    }
                },
                "roadmap": {
                    "title": "Development Roadmap",
                    "q1": {
                        "title": "Q1 2024",
                        "items": [
                            "NFT Pass Launch",
                            "Community Governance Launch",
                            "Core Features Release"
                        ]
                    }
                },
                "community": {
                    "title": "Join Our Community",
                    "subtitle": "Connect with like-minded individuals"
                }
            },
            zh: {
                "mint": {
                    "title": "铸造您的通行证",
                    "subtitle": "专属社区会员通行证",
                    "description": "加入专属的AkashaDao社区",
                    "whitelist": {
                        "benefits": [
                            "治理投票权",
                            "高级社区访问权限",
                            "新功能抢先体验",
                            "专属活动邀请"
                        ]
                    }
                },
                "roadmap": {
                    "title": "发展路线图",
                    "q1": {
                        "title": "第一季度 2024",
                        "items": [
                            "NFT通行证发布",
                            "社区治理启动",
                            "基础功能上线"
                        ]
                    }
                },
                "community": {
                    "title": "加入我们的社区",
                    "subtitle": "与志同道合的人建立联系"
                }
            }
        };

        let currentLang = 'zh';

        function t(key) {
            const keys = key.split('.');
            let value = translations[currentLang];
            
            for (const k of keys) {
                if (value && typeof value === 'object' && k in value) {
                    value = value[k];
                } else {
                    return key;
                }
            }
            
            return typeof value === 'string' ? value : key;
        }

        function tArray(key) {
            const keys = key.split('.');
            let value = translations[currentLang];
            
            for (const k of keys) {
                if (value && typeof value === 'object' && k in value) {
                    value = value[k];
                } else {
                    return [];
                }
            }
            
            return Array.isArray(value) ? value : [];
        }

        function testTranslations() {
            const results = document.getElementById('translation-results');
            let html = '<h4>翻译测试结果 / Translation Test Results:</h4>';

            // 测试字符串翻译
            const titleEn = t('mint.title');
            html += `<p class="${titleEn === 'Mint Your Pass' ? 'success' : 'error'}">
                English Mint Title: ${titleEn} ${titleEn === 'Mint Your Pass' ? '✓' : '✗'}
            </p>`;

            // 测试数组翻译
            const benefitsEn = tArray('mint.whitelist.benefits');
            html += `<p class="${benefitsEn.length === 4 ? 'success' : 'error'}">
                English Benefits Array: ${benefitsEn.length} items ${benefitsEn.length === 4 ? '✓' : '✗'}
            </p>`;

            // 测试路线图翻译
            const roadmapTitle = t('roadmap.title');
            html += `<p class="${roadmapTitle.includes('Roadmap') || roadmapTitle.includes('路线图') ? 'success' : 'error'}">
                Roadmap Title: ${roadmapTitle} ${roadmapTitle.includes('Roadmap') || roadmapTitle.includes('路线图') ? '✓' : '✗'}
            </p>`;

            // 测试路线图数组翻译
            const roadmapItems = tArray('roadmap.q1.items');
            html += `<p class="${roadmapItems.length === 3 ? 'success' : 'error'}">
                Roadmap Q1 Items: ${roadmapItems.length} items ${roadmapItems.length === 3 ? '✓' : '✗'}
            </p>`;

            // 测试社区翻译
            const communityTitle = t('community.title');
            html += `<p class="${communityTitle.includes('Community') || communityTitle.includes('社区') ? 'success' : 'error'}">
                Community Title: ${communityTitle} ${communityTitle.includes('Community') || communityTitle.includes('社区') ? '✓' : '✗'}
            </p>`;

            results.innerHTML = html;
        }

        function switchToEnglish() {
            currentLang = 'en';
            updateContent();
            document.getElementById('lang-display').textContent = 'en';
        }

        function switchToChinese() {
            currentLang = 'zh';
            updateContent();
            document.getElementById('lang-display').textContent = 'zh';
        }

        function updateContent() {
            document.getElementById('preview-title').textContent = t('mint.title');
            document.getElementById('preview-subtitle').textContent = t('mint.subtitle');
            document.getElementById('preview-description').textContent = t('mint.description');

            const benefitsList = document.getElementById('preview-benefits');
            const benefits = tArray('mint.whitelist.benefits');
            benefitsList.innerHTML = benefits.map(benefit => `<li>${benefit}</li>`).join('');

            // 更新路线图和社区内容
            const roadmapTitle = document.getElementById('roadmap-title');
            const communityTitle = document.getElementById('community-title');
            const roadmapItems = document.getElementById('roadmap-items');

            if (roadmapTitle) roadmapTitle.textContent = t('roadmap.title');
            if (communityTitle) communityTitle.textContent = t('community.title');

            if (roadmapItems) {
                const items = tArray('roadmap.q1.items');
                roadmapItems.innerHTML = items.map(item => `<li>${item}</li>`).join('');
            }
        }

        // 初始化
        updateContent();
    </script>
</body>
</html>
