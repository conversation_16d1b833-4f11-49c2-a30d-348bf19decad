#!/bin/bash

echo "🚀 启动 AkashaDao NFT 前端开发服务器..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node_version=$(node -v)
echo "当前 Node.js 版本: $node_version"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "⚙️  创建环境变量文件..."
    cat > .env.local << EOF
# Web3配置
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id
NEXT_PUBLIC_AKASHA_NFT_ADDRESS=******************************************
NEXT_PUBLIC_ACCESS_CONTROL_ADDRESS=******************************************
NEXT_PUBLIC_MERKLE_GENERATOR_ADDRESS=******************************************

# 网络配置
NEXT_PUBLIC_CHAIN_ID=31337
NEXT_PUBLIC_RPC_URL=http://localhost:8545
EOF
    echo "✅ 环境变量文件已创建"
fi

# 启动开发服务器
echo "🌐 启动开发服务器..."
echo "📱 前端地址: http://localhost:3000"
echo "🎨 铸造页面: http://localhost:3000/mint"
echo "🧪 测试页面: test-components.html"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 尝试不同的启动方式
if command -v npx &> /dev/null; then
    npx next dev
elif [ -f "node_modules/.bin/next" ]; then
    ./node_modules/.bin/next dev
else
    echo "❌ 无法找到 Next.js，请检查安装"
    echo "尝试运行: npm install next@latest"
fi
