import { expect } from "chai";
import { ethers } from "hardhat";
import { MerkleTreeGenerator } from "../typechain-types";
import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { MerkleTree } from "merkletreejs";

describe("MerkleTreeGenerator", function () {
  let merkleGenerator: MerkleTreeGenerator;
  let owner: Signer<PERSON>ithAddress;
  let user1: SignerWithAddress;
  let user2: Signer<PERSON>ithAddress;
  let user3: SignerWithAddress;
  let addrs: SignerWithAddress[];

  let merkleTree: MerkleTree;
  let merkleRoot: string;
  let testAddresses: string[];

  beforeEach(async function () {
    [owner, user1, user2, user3, ...addrs] = await ethers.getSigners();

    // 部署合约
    const MerkleTreeGenerator = await ethers.getContractFactory("MerkleTreeGenerator");
    merkleGenerator = await MerkleTreeGenerator.deploy();
    await merkleGenerator.waitForDeployment();

    // 准备测试数据
    testAddresses = [user1.address, user2.address, user3.address];
    
    // 创建Merkle Tree
    const leaves = testAddresses.map(addr => 
      ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
    );
    merkleTree = new MerkleTree(leaves, ethers.keccak256, { sortPairs: true });
    merkleRoot = merkleTree.getHexRoot();
  });

  describe("部署", function () {
    it("应该正确设置owner", async function () {
      expect(await merkleGenerator.owner()).to.equal(owner.address);
    });
  });

  describe("Merkle Root管理", function () {
    it("应该允许owner设置Merkle Root", async function () {
      await expect(
        merkleGenerator.setMerkleRoot("whitelist", merkleRoot)
      ).to.emit(merkleGenerator, "MerkleRootSet")
       .withArgs("whitelist", merkleRoot);

      expect(await merkleGenerator.getMerkleRoot("whitelist")).to.equal(merkleRoot);
    });

    it("应该拒绝非owner设置Merkle Root", async function () {
      await expect(
        merkleGenerator.connect(user1).setMerkleRoot("whitelist", merkleRoot)
      ).to.be.revertedWithCustomError(merkleGenerator, "OwnableUnauthorizedAccount");
    });

    it("应该允许owner删除Merkle Root", async function () {
      // 先设置
      await merkleGenerator.setMerkleRoot("whitelist", merkleRoot);
      
      // 再删除
      await expect(
        merkleGenerator.removeMerkleRoot("whitelist")
      ).to.emit(merkleGenerator, "MerkleRootSet")
       .withArgs("whitelist", ethers.ZeroHash);

      expect(await merkleGenerator.getMerkleRoot("whitelist")).to.equal(ethers.ZeroHash);
    });
  });

  describe("地址验证", function () {
    beforeEach(async function () {
      await merkleGenerator.setMerkleRoot("whitelist", merkleRoot);
    });

    it("应该正确验证有效地址", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [user1.address]));
      const proof = merkleTree.getHexProof(leaf);

      expect(await merkleGenerator.verifyAddress("whitelist", user1.address, proof)).to.be.true;
    });

    it("应该拒绝无效地址", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [addrs[0].address]));
      const proof = merkleTree.getHexProof(leaf);

      expect(await merkleGenerator.verifyAddress("whitelist", addrs[0].address, proof)).to.be.false;
    });

    it("应该拒绝错误的proof", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [user2.address]));
      const proof = merkleTree.getHexProof(leaf);

      // 使用user1的地址但是user2的proof
      expect(await merkleGenerator.verifyAddress("whitelist", user1.address, proof)).to.be.false;
    });

    it("应该拒绝未设置Merkle Root的列表类型", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [user1.address]));
      const proof = merkleTree.getHexProof(leaf);

      await expect(
        merkleGenerator.verifyAddress("nonexistent", user1.address, proof)
      ).to.be.revertedWith("Merkle root not set for this list type");
    });
  });

  describe("批量验证", function () {
    beforeEach(async function () {
      await merkleGenerator.setMerkleRoot("whitelist", merkleRoot);
    });

    it("应该正确批量验证地址", async function () {
      const users = [user1.address, user2.address, addrs[0].address];
      const proofs = users.map(addr => {
        const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [addr]));
        return merkleTree.getHexProof(leaf);
      });

      const results = await merkleGenerator.verifyAddresses("whitelist", users, proofs);
      
      expect(results[0]).to.be.true;  // user1 应该有效
      expect(results[1]).to.be.true;  // user2 应该有效
      expect(results[2]).to.be.false; // addrs[0] 应该无效
    });

    it("应该拒绝数组长度不匹配", async function () {
      const users = [user1.address, user2.address];
      const proofs = [[]]; // 只有一个proof

      await expect(
        merkleGenerator.verifyAddresses("whitelist", users, proofs)
      ).to.be.revertedWith("Arrays length mismatch");
    });
  });

  describe("验证缓存", function () {
    beforeEach(async function () {
      await merkleGenerator.setMerkleRoot("whitelist", merkleRoot);
    });

    it("应该允许标记地址为已验证", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [user1.address]));
      const proof = merkleTree.getHexProof(leaf);

      await expect(
        merkleGenerator.markAsVerified("whitelist", user1.address, proof)
      ).to.emit(merkleGenerator, "AddressVerified")
       .withArgs("whitelist", user1.address);

      expect(await merkleGenerator.isVerified("whitelist", user1.address)).to.be.true;
    });

    it("应该拒绝标记无效地址为已验证", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [addrs[0].address]));
      const proof = merkleTree.getHexProof(leaf);

      await expect(
        merkleGenerator.markAsVerified("whitelist", addrs[0].address, proof)
      ).to.be.revertedWith("Invalid proof");
    });

    it("应该允许owner清除验证缓存", async function () {
      // 先标记为已验证
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [user1.address]));
      const proof = merkleTree.getHexProof(leaf);
      await merkleGenerator.markAsVerified("whitelist", user1.address, proof);

      expect(await merkleGenerator.isVerified("whitelist", user1.address)).to.be.true;

      // 清除缓存
      await merkleGenerator.clearVerificationCache("whitelist", [user1.address]);

      expect(await merkleGenerator.isVerified("whitelist", user1.address)).to.be.false;
    });

    it("应该拒绝非owner清除验证缓存", async function () {
      await expect(
        merkleGenerator.connect(user1).clearVerificationCache("whitelist", [user1.address])
      ).to.be.revertedWithCustomError(merkleGenerator, "OwnableUnauthorizedAccount");
    });
  });

  describe("工具函数", function () {
    it("应该正确生成叶子节点哈希", async function () {
      const expectedLeaf = ethers.keccak256(ethers.solidityPacked(["address"], [user1.address]));
      const actualLeaf = await merkleGenerator.generateLeaf(user1.address);

      expect(actualLeaf).to.equal(expectedLeaf);
    });

    it("应该正确批量生成叶子节点哈希", async function () {
      const expectedLeaves = testAddresses.map(addr => 
        ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
      );
      const actualLeaves = await merkleGenerator.generateLeaves(testAddresses);

      expect(actualLeaves.length).to.equal(expectedLeaves.length);
      for (let i = 0; i < expectedLeaves.length; i++) {
        expect(actualLeaves[i]).to.equal(expectedLeaves[i]);
      }
    });
  });

  describe("多种列表类型", function () {
    it("应该支持多种列表类型", async function () {
      // 设置不同的Merkle Root
      await merkleGenerator.setMerkleRoot("whitelist", merkleRoot);
      await merkleGenerator.setMerkleRoot("lottery", merkleRoot);
      await merkleGenerator.setMerkleRoot("student", merkleRoot);

      expect(await merkleGenerator.getMerkleRoot("whitelist")).to.equal(merkleRoot);
      expect(await merkleGenerator.getMerkleRoot("lottery")).to.equal(merkleRoot);
      expect(await merkleGenerator.getMerkleRoot("student")).to.equal(merkleRoot);
    });

    it("应该独立管理不同列表的验证状态", async function () {
      await merkleGenerator.setMerkleRoot("whitelist", merkleRoot);
      await merkleGenerator.setMerkleRoot("student", merkleRoot);

      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [user1.address]));
      const proof = merkleTree.getHexProof(leaf);

      // 只在whitelist中标记为已验证
      await merkleGenerator.markAsVerified("whitelist", user1.address, proof);

      expect(await merkleGenerator.isVerified("whitelist", user1.address)).to.be.true;
      expect(await merkleGenerator.isVerified("student", user1.address)).to.be.false;
    });
  });
});
