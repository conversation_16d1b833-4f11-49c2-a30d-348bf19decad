import { expect } from "chai";
import { ethers } from "hardhat";
import { AkashaDaoNFT, MerkleTreeGenerator } from "../typechain-types";
import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { MerkleTree } from "merkletreejs";

describe("AkashaDaoNFT", function () {
  let akashaNFT: AkashaDaoNFT;
  let merkleGenerator: MerkleTreeGenerator;
  let owner: Signer<PERSON>ithAddress;
  let whitelistUser1: SignerWithAddress;
  let whitelistUser2: SignerWithAddress;
  let lotteryUser1: SignerWithAddress;
  let studentUser1: SignerWithAddress;
  let regularUser: SignerWithAddress;
  let addrs: Signer<PERSON>ithAddress[];

  let whitelistMerkleTree: MerkleTree;
  let lotteryMerkleTree: MerkleTree;
  let studentMerkleTree: MerkleTree;
  let whitelistRoot: string;
  let lotteryRoot: string;
  let studentRoot: string;

  const whitelistTokenURI = "https://akasha-dao.com/metadata/whitelist.json";
  const studentTokenURI = "https://akasha-dao.com/metadata/student.json";

  beforeEach(async function () {
    // 获取签名者
    [owner, whitelistUser1, whitelistUser2, lotteryUser1, studentUser1, regularUser, ...addrs] = 
      await ethers.getSigners();

    // 部署合约
    const AkashaDaoNFT = await ethers.getContractFactory("AkashaDaoNFT");
    akashaNFT = await AkashaDaoNFT.deploy(whitelistTokenURI, studentTokenURI);
    await akashaNFT.waitForDeployment();

    const MerkleTreeGenerator = await ethers.getContractFactory("MerkleTreeGenerator");
    merkleGenerator = await MerkleTreeGenerator.deploy();
    await merkleGenerator.waitForDeployment();

    // 创建Merkle Trees
    const whitelistAddresses = [whitelistUser1.address, whitelistUser2.address];
    const lotteryAddresses = [lotteryUser1.address];
    const studentAddresses = [studentUser1.address];

    // 生成白名单Merkle Tree
    const whitelistLeaves = whitelistAddresses.map(addr => 
      ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
    );
    whitelistMerkleTree = new MerkleTree(whitelistLeaves, ethers.keccak256, { sortPairs: true });
    whitelistRoot = whitelistMerkleTree.getHexRoot();

    // 生成抽奖Merkle Tree
    const lotteryLeaves = lotteryAddresses.map(addr => 
      ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
    );
    lotteryMerkleTree = new MerkleTree(lotteryLeaves, ethers.keccak256, { sortPairs: true });
    lotteryRoot = lotteryMerkleTree.getHexRoot();

    // 生成学生Merkle Tree
    const studentLeaves = studentAddresses.map(addr => 
      ethers.keccak256(ethers.solidityPacked(["address"], [addr]))
    );
    studentMerkleTree = new MerkleTree(studentLeaves, ethers.keccak256, { sortPairs: true });
    studentRoot = studentMerkleTree.getHexRoot();

    // 设置Merkle Roots
    await akashaNFT.setWhitelistMerkleRoot(whitelistRoot);
    await akashaNFT.setLotteryMerkleRoot(lotteryRoot);
    await akashaNFT.setStudentMerkleRoot(studentRoot);
  });

  describe("部署", function () {
    it("应该正确设置合约名称和符号", async function () {
      expect(await akashaNFT.name()).to.equal("AkashaDao Community Pass");
      expect(await akashaNFT.symbol()).to.equal("AKASHA");
    });

    it("应该正确设置owner", async function () {
      expect(await akashaNFT.owner()).to.equal(owner.address);
    });

    it("应该正确设置初始价格", async function () {
      expect(await akashaNFT.whitelistPrice()).to.equal(ethers.parseEther("0.05"));
      expect(await akashaNFT.publicPrice()).to.equal(ethers.parseEther("0.08"));
      expect(await akashaNFT.studentPrice()).to.equal(ethers.parseEther("0.03"));
    });

    it("应该正确设置初始供应量", async function () {
      expect(await akashaNFT.totalSupply()).to.equal(0);
      expect(await akashaNFT.whitelistMinted()).to.equal(0);
      expect(await akashaNFT.studentMinted()).to.equal(0);
    });
  });

  describe("白名单Mint", function () {
    beforeEach(async function () {
      await akashaNFT.setWhitelistSaleActive(true);
    });

    it("应该允许白名单用户mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser1.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
          value: ethers.parseEther("0.05")
        })
      ).to.emit(akashaNFT, "WhitelistMint")
       .withArgs(whitelistUser1.address, 0);

      expect(await akashaNFT.balanceOf(whitelistUser1.address)).to.equal(1);
      expect(await akashaNFT.ownerOf(0)).to.equal(whitelistUser1.address);
      expect(await akashaNFT.whitelistMinted()).to.equal(1);
    });

    it("应该拒绝非白名单用户mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [regularUser.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(regularUser).whitelistMint(proof, {
          value: ethers.parseEther("0.05")
        })
      ).to.be.revertedWith("Invalid whitelist proof");
    });

    it("应该拒绝支付不足的mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser1.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
          value: ethers.parseEther("0.04")
        })
      ).to.be.revertedWith("Insufficient payment");
    });

    it("应该拒绝重复mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser1.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      // 第一次mint
      await akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
        value: ethers.parseEther("0.05")
      });

      // 第二次mint应该失败
      await expect(
        akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
          value: ethers.parseEther("0.05")
        })
      ).to.be.revertedWith("Already claimed whitelist");
    });

    it("应该拒绝销售未激活时的mint", async function () {
      await akashaNFT.setWhitelistSaleActive(false);
      
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser1.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
          value: ethers.parseEther("0.05")
        })
      ).to.be.revertedWith("Whitelist sale not active");
    });
  });

  describe("抽奖Mint", function () {
    beforeEach(async function () {
      await akashaNFT.setLotterySaleActive(true);
    });

    it("应该允许抽奖用户mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [lotteryUser1.address]));
      const proof = lotteryMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(lotteryUser1).lotteryMint(proof, {
          value: ethers.parseEther("0.08")
        })
      ).to.emit(akashaNFT, "LotteryMint")
       .withArgs(lotteryUser1.address, 0);

      expect(await akashaNFT.balanceOf(lotteryUser1.address)).to.equal(1);
      expect(await akashaNFT.whitelistMinted()).to.equal(1);
    });

    it("应该拒绝非抽奖用户mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [regularUser.address]));
      const proof = lotteryMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(regularUser).lotteryMint(proof, {
          value: ethers.parseEther("0.08")
        })
      ).to.be.revertedWith("Invalid lottery proof");
    });
  });

  describe("学生Mint", function () {
    beforeEach(async function () {
      await akashaNFT.setStudentSaleActive(true);
    });

    it("应该允许学生用户mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [studentUser1.address]));
      const proof = studentMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(studentUser1).studentMint(proof, {
          value: ethers.parseEther("0.03")
        })
      ).to.emit(akashaNFT, "StudentMint")
       .withArgs(studentUser1.address, 0);

      expect(await akashaNFT.balanceOf(studentUser1.address)).to.equal(1);
      expect(await akashaNFT.studentMinted()).to.equal(1);
    });

    it("应该拒绝非学生用户mint", async function () {
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [regularUser.address]));
      const proof = studentMerkleTree.getHexProof(leaf);

      await expect(
        akashaNFT.connect(regularUser).studentMint(proof, {
          value: ethers.parseEther("0.03")
        })
      ).to.be.revertedWith("Invalid student proof");
    });
  });

  describe("NFT信息查询", function () {
    it("应该正确返回白名单NFT信息", async function () {
      await akashaNFT.setWhitelistSaleActive(true);
      
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser1.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      await akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
        value: ethers.parseEther("0.05")
      });

      const nftInfo = await akashaNFT.getNFTInfo(0);
      expect(nftInfo.tier).to.equal(0); // WHITELIST
      expect(nftInfo.hasVotingRights).to.be.true;
      expect(nftInfo.hasUnlimitedAccess).to.be.true;
    });

    it("应该正确返回学生NFT信息", async function () {
      await akashaNFT.setStudentSaleActive(true);
      
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [studentUser1.address]));
      const proof = studentMerkleTree.getHexProof(leaf);

      await akashaNFT.connect(studentUser1).studentMint(proof, {
        value: ethers.parseEther("0.03")
      });

      const nftInfo = await akashaNFT.getNFTInfo(0);
      expect(nftInfo.tier).to.equal(1); // STUDENT
      expect(nftInfo.hasVotingRights).to.be.false;
      expect(nftInfo.hasUnlimitedAccess).to.be.true;
    });
  });

  describe("管理员功能", function () {
    it("应该允许owner更新价格", async function () {
      await akashaNFT.updatePrices(
        ethers.parseEther("0.06"),
        ethers.parseEther("0.09"),
        ethers.parseEther("0.04")
      );

      expect(await akashaNFT.whitelistPrice()).to.equal(ethers.parseEther("0.06"));
      expect(await akashaNFT.publicPrice()).to.equal(ethers.parseEther("0.09"));
      expect(await akashaNFT.studentPrice()).to.equal(ethers.parseEther("0.04"));
    });

    it("应该拒绝非owner更新价格", async function () {
      await expect(
        akashaNFT.connect(regularUser).updatePrices(
          ethers.parseEther("0.06"),
          ethers.parseEther("0.09"),
          ethers.parseEther("0.04")
        )
      ).to.be.revertedWithCustomError(akashaNFT, "OwnableUnauthorizedAccount");
    });

    it("应该允许owner提取资金", async function () {
      // 先进行一些mint操作
      await akashaNFT.setWhitelistSaleActive(true);
      const leaf = ethers.keccak256(ethers.solidityPacked(["address"], [whitelistUser1.address]));
      const proof = whitelistMerkleTree.getHexProof(leaf);

      await akashaNFT.connect(whitelistUser1).whitelistMint(proof, {
        value: ethers.parseEther("0.05")
      });

      const initialBalance = await ethers.provider.getBalance(owner.address);
      const tx = await akashaNFT.withdraw();
      const receipt = await tx.wait();
      const gasUsed = receipt!.gasUsed * receipt!.gasPrice;
      const finalBalance = await ethers.provider.getBalance(owner.address);

      expect(finalBalance).to.be.closeTo(
        initialBalance + ethers.parseEther("0.05") - gasUsed,
        ethers.parseEther("0.001")
      );
    });
  });

  describe("供应量限制", function () {
    it("应该正确计算剩余供应量", async function () {
      expect(await akashaNFT.remainingWhitelistSupply()).to.equal(80); // 100 * 80%
      expect(await akashaNFT.remainingLotterySupply()).to.equal(20); // 100 * 20%
      expect(await akashaNFT.remainingStudentSupply()).to.equal(500);
    });
  });
});
