// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "./AkashaDaoNFT.sol";

/**
 * @title AkashaDaoAccessControl
 * @dev 权益验证和访问控制合约
 * 用于验证NFT持有者的各种权益
 */
contract AkashaDaoAccessControl is Ownable {
    
    AkashaDaoNFT public immutable akashaNFT;
    
    // 权益类型枚举
    enum AccessType {
        UNLIMITED_READING,    // 无限阅读权限
        VOTING_RIGHTS,        // 治理投票权
        EXCLUSIVE_EVENTS,     // 专属活动参与权
        VC_NETWORKING,        // VC资源对接权
        PREMIUM_CONTENT       // 高级内容访问权
    }
    
    // 活动信息结构体
    struct Event {
        string name;
        string description;
        uint256 startTime;
        uint256 endTime;
        bool requiresWhitelist;
        bool isActive;
        mapping(address => bool) participants;
        address[] participantList;
    }
    
    // 投票信息结构体
    struct Proposal {
        string title;
        string description;
        uint256 startTime;
        uint256 endTime;
        uint256 forVotes;
        uint256 againstVotes;
        bool executed;
        mapping(address => bool) hasVoted;
        mapping(address => bool) voteChoice; // true for, false against
    }
    
    // 存储
    mapping(uint256 => Event) public events;
    mapping(uint256 => Proposal) public proposals;
    mapping(address => uint256) public lastAccessTime;
    mapping(address => mapping(AccessType => bool)) public accessRights;
    
    uint256 public eventCounter = 0;
    uint256 public proposalCounter = 0;
    
    // 事件
    event AccessGranted(address indexed user, AccessType accessType);
    event AccessRevoked(address indexed user, AccessType accessType);
    event EventCreated(uint256 indexed eventId, string name);
    event EventParticipation(uint256 indexed eventId, address indexed participant);
    event ProposalCreated(uint256 indexed proposalId, string title);
    event VoteCast(uint256 indexed proposalId, address indexed voter, bool choice);
    event ProposalExecuted(uint256 indexed proposalId);
    
    constructor(address _akashaNFT) Ownable(msg.sender) {
        akashaNFT = AkashaDaoNFT(_akashaNFT);
    }
    
    /**
     * @dev 验证用户是否有特定访问权限
     */
    function hasAccess(address user, AccessType accessType) public view returns (bool) {
        // 检查用户是否持有NFT
        uint256 balance = akashaNFT.balanceOf(user);
        if (balance == 0) return false;
        
        // 获取用户的第一个NFT信息
        for (uint256 i = 0; i < akashaNFT.totalSupply(); i++) {
            try akashaNFT.ownerOf(i) returns (address owner) {
                if (owner == user) {
                    AkashaDaoNFT.NFTInfo memory info = akashaNFT.getNFTInfo(i);
                    
                    if (accessType == AccessType.UNLIMITED_READING) {
                        return info.hasUnlimitedAccess;
                    } else if (accessType == AccessType.VOTING_RIGHTS) {
                        return info.hasVotingRights;
                    } else if (accessType == AccessType.EXCLUSIVE_EVENTS) {
                        return info.tier == AkashaDaoNFT.NFTTier.WHITELIST;
                    } else if (accessType == AccessType.VC_NETWORKING) {
                        return true; // 所有NFT持有者都有VC对接权
                    } else if (accessType == AccessType.PREMIUM_CONTENT) {
                        return info.tier == AkashaDaoNFT.NFTTier.WHITELIST;
                    }
                }
            } catch {
                continue;
            }
        }
        
        return false;
    }
    
    /**
     * @dev 验证用户是否为白名单成员
     */
    function isWhitelistMember(address user) public view returns (bool) {
        uint256 balance = akashaNFT.balanceOf(user);
        if (balance == 0) return false;
        
        for (uint256 i = 0; i < akashaNFT.totalSupply(); i++) {
            try akashaNFT.ownerOf(i) returns (address owner) {
                if (owner == user) {
                    AkashaDaoNFT.NFTInfo memory info = akashaNFT.getNFTInfo(i);
                    if (info.tier == AkashaDaoNFT.NFTTier.WHITELIST) {
                        return true;
                    }
                }
            } catch {
                continue;
            }
        }
        
        return false;
    }
    
    /**
     * @dev 验证用户是否为学生成员
     */
    function isStudentMember(address user) public view returns (bool) {
        uint256 balance = akashaNFT.balanceOf(user);
        if (balance == 0) return false;
        
        for (uint256 i = 0; i < akashaNFT.totalSupply(); i++) {
            try akashaNFT.ownerOf(i) returns (address owner) {
                if (owner == user) {
                    AkashaDaoNFT.NFTInfo memory info = akashaNFT.getNFTInfo(i);
                    if (info.tier == AkashaDaoNFT.NFTTier.STUDENT) {
                        return true;
                    }
                }
            } catch {
                continue;
            }
        }
        
        return false;
    }
    
    /**
     * @dev 创建活动
     */
    function createEvent(
        string memory name,
        string memory description,
        uint256 startTime,
        uint256 endTime,
        bool requiresWhitelist
    ) external onlyOwner {
        require(startTime < endTime, "Invalid time range");
        require(endTime > block.timestamp, "End time must be in future");
        
        Event storage newEvent = events[eventCounter];
        newEvent.name = name;
        newEvent.description = description;
        newEvent.startTime = startTime;
        newEvent.endTime = endTime;
        newEvent.requiresWhitelist = requiresWhitelist;
        newEvent.isActive = true;
        
        emit EventCreated(eventCounter, name);
        eventCounter++;
    }
    
    /**
     * @dev 参与活动
     */
    function participateInEvent(uint256 eventId) external {
        require(eventId < eventCounter, "Event does not exist");
        Event storage eventInfo = events[eventId];
        require(eventInfo.isActive, "Event is not active");
        require(block.timestamp >= eventInfo.startTime, "Event has not started");
        require(block.timestamp <= eventInfo.endTime, "Event has ended");
        require(!eventInfo.participants[msg.sender], "Already participating");
        
        // 检查权限
        if (eventInfo.requiresWhitelist) {
            require(isWhitelistMember(msg.sender), "Whitelist membership required");
        } else {
            require(akashaNFT.balanceOf(msg.sender) > 0, "NFT ownership required");
        }
        
        eventInfo.participants[msg.sender] = true;
        eventInfo.participantList.push(msg.sender);
        
        emit EventParticipation(eventId, msg.sender);
    }
    
    /**
     * @dev 创建提案
     */
    function createProposal(
        string memory title,
        string memory description,
        uint256 votingPeriod
    ) external {
        require(isWhitelistMember(msg.sender), "Only whitelist members can create proposals");
        require(votingPeriod > 0, "Voting period must be positive");
        
        Proposal storage newProposal = proposals[proposalCounter];
        newProposal.title = title;
        newProposal.description = description;
        newProposal.startTime = block.timestamp;
        newProposal.endTime = block.timestamp + votingPeriod;
        newProposal.executed = false;
        
        emit ProposalCreated(proposalCounter, title);
        proposalCounter++;
    }
    
    /**
     * @dev 投票
     */
    function vote(uint256 proposalId, bool choice) external {
        require(proposalId < proposalCounter, "Proposal does not exist");
        require(hasAccess(msg.sender, AccessType.VOTING_RIGHTS), "No voting rights");
        
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp >= proposal.startTime, "Voting has not started");
        require(block.timestamp <= proposal.endTime, "Voting has ended");
        require(!proposal.hasVoted[msg.sender], "Already voted");
        
        proposal.hasVoted[msg.sender] = true;
        proposal.voteChoice[msg.sender] = choice;
        
        if (choice) {
            proposal.forVotes++;
        } else {
            proposal.againstVotes++;
        }
        
        emit VoteCast(proposalId, msg.sender, choice);
    }
    
    /**
     * @dev 获取活动参与者列表
     */
    function getEventParticipants(uint256 eventId) external view returns (address[] memory) {
        require(eventId < eventCounter, "Event does not exist");
        return events[eventId].participantList;
    }
    
    /**
     * @dev 获取提案投票结果
     */
    function getProposalResults(uint256 proposalId) external view returns (
        uint256 forVotes,
        uint256 againstVotes,
        bool executed
    ) {
        require(proposalId < proposalCounter, "Proposal does not exist");
        Proposal storage proposal = proposals[proposalId];
        return (proposal.forVotes, proposal.againstVotes, proposal.executed);
    }
    
    /**
     * @dev 检查用户是否已投票
     */
    function hasVoted(uint256 proposalId, address user) external view returns (bool) {
        require(proposalId < proposalCounter, "Proposal does not exist");
        return proposals[proposalId].hasVoted[user];
    }
    
    /**
     * @dev 获取用户投票选择
     */
    function getUserVote(uint256 proposalId, address user) external view returns (bool) {
        require(proposalId < proposalCounter, "Proposal does not exist");
        require(proposals[proposalId].hasVoted[user], "User has not voted");
        return proposals[proposalId].voteChoice[user];
    }
    
    /**
     * @dev 更新访问时间
     */
    function updateAccessTime(address user) external {
        require(akashaNFT.balanceOf(user) > 0, "NFT ownership required");
        lastAccessTime[user] = block.timestamp;
    }
    
    /**
     * @dev 管理员功能：设置活动状态
     */
    function setEventActive(uint256 eventId, bool active) external onlyOwner {
        require(eventId < eventCounter, "Event does not exist");
        events[eventId].isActive = active;
    }
    
    /**
     * @dev 管理员功能：执行提案
     */
    function executeProposal(uint256 proposalId) external onlyOwner {
        require(proposalId < proposalCounter, "Proposal does not exist");
        Proposal storage proposal = proposals[proposalId];
        require(block.timestamp > proposal.endTime, "Voting period not ended");
        require(!proposal.executed, "Proposal already executed");
        require(proposal.forVotes > proposal.againstVotes, "Proposal rejected");
        
        proposal.executed = true;
        emit ProposalExecuted(proposalId);
    }
}
