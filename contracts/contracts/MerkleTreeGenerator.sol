// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title MerkleTreeGenerator
 * @dev 用于生成和验证Merkle Tree的工具合约
 * 支持白名单、抽奖和学生名单的管理
 */
contract MerkleTreeGenerator is Ownable {

    // 存储不同类型的Merkle Root
    mapping(string => bytes32) public merkleRoots;
    mapping(string => mapping(address => bool)) public verifiedAddresses;

    // 事件
    event MerkleRootSet(string indexed listType, bytes32 root);
    event AddressVerified(string indexed listType, address indexed user);

    constructor() Ownable(msg.sender) {}
    
    /**
     * @dev 设置指定类型的Merkle Root
     */
    function setMerkleRoot(string memory listType, bytes32 root) external onlyOwner {
        merkleRoots[listType] = root;
        emit MerkleRootSet(listType, root);
    }
    
    /**
     * @dev 验证地址是否在指定列表中
     */
    function verifyAddress(
        string memory listType,
        address user,
        bytes32[] calldata merkleProof
    ) external view returns (bool) {
        bytes32 root = merkleRoots[listType];
        require(root != bytes32(0), "Merkle root not set for this list type");
        
        bytes32 leaf = keccak256(abi.encodePacked(user));
        return MerkleProof.verify(merkleProof, root, leaf);
    }
    
    /**
     * @dev 批量验证地址
     */
    function verifyAddresses(
        string memory listType,
        address[] calldata users,
        bytes32[][] calldata merkleProofs
    ) external view returns (bool[] memory) {
        require(users.length == merkleProofs.length, "Arrays length mismatch");
        
        bytes32 root = merkleRoots[listType];
        require(root != bytes32(0), "Merkle root not set for this list type");
        
        bool[] memory results = new bool[](users.length);
        
        for (uint256 i = 0; i < users.length; i++) {
            bytes32 leaf = keccak256(abi.encodePacked(users[i]));
            results[i] = MerkleProof.verify(merkleProofs[i], root, leaf);
        }
        
        return results;
    }
    
    /**
     * @dev 标记地址为已验证（用于缓存）
     */
    function markAsVerified(
        string memory listType,
        address user,
        bytes32[] calldata merkleProof
    ) external {
        require(this.verifyAddress(listType, user, merkleProof), "Invalid proof");
        verifiedAddresses[listType][user] = true;
        emit AddressVerified(listType, user);
    }
    
    /**
     * @dev 检查地址是否已被标记为验证
     */
    function isVerified(string memory listType, address user) external view returns (bool) {
        return verifiedAddresses[listType][user];
    }
    
    /**
     * @dev 获取指定类型的Merkle Root
     */
    function getMerkleRoot(string memory listType) external view returns (bytes32) {
        return merkleRoots[listType];
    }
    
    /**
     * @dev 生成叶子节点哈希
     */
    function generateLeaf(address user) external pure returns (bytes32) {
        return keccak256(abi.encodePacked(user));
    }
    
    /**
     * @dev 批量生成叶子节点哈希
     */
    function generateLeaves(address[] calldata users) external pure returns (bytes32[] memory) {
        bytes32[] memory leaves = new bytes32[](users.length);
        for (uint256 i = 0; i < users.length; i++) {
            leaves[i] = keccak256(abi.encodePacked(users[i]));
        }
        return leaves;
    }
    
    /**
     * @dev 清除指定类型的验证缓存
     */
    function clearVerificationCache(string memory listType, address[] calldata users) external onlyOwner {
        for (uint256 i = 0; i < users.length; i++) {
            verifiedAddresses[listType][users[i]] = false;
        }
    }
    
    /**
     * @dev 删除指定类型的Merkle Root
     */
    function removeMerkleRoot(string memory listType) external onlyOwner {
        delete merkleRoots[listType];
        emit MerkleRootSet(listType, bytes32(0));
    }
}
