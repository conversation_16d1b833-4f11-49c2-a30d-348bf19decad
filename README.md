# AkashaDao社区入场券NFT项目

## 项目概述

AkashaDao社区入场券NFT是一个基于以太坊的NFT项目，旨在为社区成员提供分层权益凭证，维持IP神秘人设，基于信任关系构建价值。

## NFT分层体系

### 白名单层级（100张）
- **目标用户**：社区元老
- **权益**：免费无限阅读Akasha paragraph文章 + 核心治理权 + 最高阶专属标识
- **分发**：80%白名单mint，20%公开抽奖
- **限制**：每人限购1张，mint时享受优惠价格

### 底层（数量待定）
- **目标用户**：大学生群体
- **权益**：免费无限阅读文章 + 参与985/藤校私享会 + 创业者分享会 + VC资源对接

## 技术规格

- **区块链**：以太坊主网
- **支付代币**：ETH
- **总供应量**：100张（第一阶段）
- **每个地址限购**：1张NFT
- **标准**：ERC-721

## 项目结构

```
akasha-dao-nft/
├── contracts/          # 智能合约
├── frontend/           # 前端DApp
├── assets/            # 资源文件
│   └── images/        # NFT图片
├── docs/              # 文档
├── scripts/           # 部署和管理脚本
└── README.md
```

## 开发环境要求

- Node.js >= 22.0.0
- npm >= 10.0.0

## 快速开始

1. 安装依赖：
```bash
npm run setup
```

2. 启动开发环境：
```bash
npm run dev
```

3. 运行测试：
```bash
npm run test
```

4. 构建项目：
```bash
npm run build
```

## 功能特性

### 智能合约功能
- [x] ERC-721标准NFT合约
- [x] 白名单功能（Merkle Tree验证）
- [x] 分层权益管理
- [x] 限购机制（每地址1张）
- [x] 优惠价格机制（白名单用户）
- [x] 抽奖机制（20%份额）
- [x] 权限管理和治理功能

### 前端功能
- [x] 现代化Web3 DApp界面
- [x] 钱包连接功能（MetaMask等）
- [x] NFT mint界面
- [x] 白名单验证界面
- [x] NFT展示页面
- [x] 权益说明页面
- [x] 响应式设计

## 许可证

MIT License
